package com.geeksec.common.exception;

import com.geeksec.common.enums.ErrorCode;

/**
 * 业务异常类
 * 用于处理业务逻辑中的异常情况
 *
 * <AUTHOR>
 * @since 3.0.0
 */
public class BusinessException extends RuntimeException {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 错误代码
     */
    private final int code;
    
    /**
     * 错误信息
     */
    private final String message;
    
    /**
     * 构造函数
     * 
     * @param errorCode 错误代码枚举
     */
    public BusinessException(ErrorCode errorCode) {
        super(errorCode.getMessage());
        this.code = errorCode.getCode();
        this.message = errorCode.getMessage();
    }
    
    /**
     * 构造函数
     * 
     * @param errorCode 错误代码枚举
     * @param message 自定义错误信息
     */
    public BusinessException(ErrorCode errorCode, String message) {
        super(message);
        this.code = errorCode.getCode();
        this.message = message;
    }
    
    /**
     * 构造函数
     * 
     * @param code 错误代码
     * @param message 错误信息
     */
    public BusinessException(int code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }
    
    /**
     * 构造函数
     * 
     * @param errorCode 错误代码枚举
     * @param cause 原始异常
     */
    public BusinessException(ErrorCode errorCode, Throwable cause) {
        super(errorCode.getMessage(), cause);
        this.code = errorCode.getCode();
        this.message = errorCode.getMessage();
    }
    
    /**
     * 构造函数
     * 
     * @param errorCode 错误代码枚举
     * @param message 自定义错误信息
     * @param cause 原始异常
     */
    public BusinessException(ErrorCode errorCode, String message, Throwable cause) {
        super(message, cause);
        this.code = errorCode.getCode();
        this.message = message;
    }
    
    /**
     * 构造函数
     * 
     * @param code 错误代码
     * @param message 错误信息
     * @param cause 原始异常
     */
    public BusinessException(int code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
        this.message = message;
    }
    
    /**
     * 获取错误代码
     * 
     * @return 错误代码
     */
    public int getCode() {
        return code;
    }
    
    /**
     * 获取错误信息
     * 
     * @return 错误信息
     */
    @Override
    public String getMessage() {
        return message;
    }
    
    /**
     * 创建业务异常的静态方法
     * 
     * @param errorCode 错误代码枚举
     * @return BusinessException实例
     */
    public static BusinessException of(ErrorCode errorCode) {
        return new BusinessException(errorCode);
    }
    
    /**
     * 创建业务异常的静态方法
     * 
     * @param errorCode 错误代码枚举
     * @param message 自定义错误信息
     * @return BusinessException实例
     */
    public static BusinessException of(ErrorCode errorCode, String message) {
        return new BusinessException(errorCode, message);
    }
    
    /**
     * 创建业务异常的静态方法
     * 
     * @param code 错误代码
     * @param message 错误信息
     * @return BusinessException实例
     */
    public static BusinessException of(int code, String message) {
        return new BusinessException(code, message);
    }
    
    @Override
    public String toString() {
        return String.format("BusinessException{code=%d, message='%s'}", code, message);
    }
}