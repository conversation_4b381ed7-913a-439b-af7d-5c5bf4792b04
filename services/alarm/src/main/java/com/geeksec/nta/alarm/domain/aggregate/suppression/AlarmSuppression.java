package com.geeksec.nta.alarm.domain.aggregate.suppression;

import com.geeksec.nta.alarm.domain.valueobject.SuppressionId;
import com.geeksec.nta.alarm.domain.valueobject.SuppressionRule;
import com.geeksec.nta.alarm.domain.event.SuppressionRuleCreatedEvent;
import com.geeksec.nta.alarm.domain.event.SuppressionRuleUpdatedEvent;
import com.geeksec.nta.alarm.domain.event.SuppressionRuleDeletedEvent;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 告警抑制规则聚合根
 * 负责抑制规则的核心业务逻辑
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@Data
@EqualsAndHashCode(callSuper = false)
@Table("alarm_suppression")
public class AlarmSuppression implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 抑制规则ID
     */
    @Id(keyType = KeyType.Auto)
    @Column("id")
    private Integer id;

    /**
     * 受害者IP
     */
    @Column("victim")
    private String victim;

    /**
     * 攻击者IP
     */
    @Column("attacker")
    private String attacker;

    /**
     * 告警相关标签
     */
    @Column("label")
    private String label;

    /**
     * 规则描述
     */
    @Column("description")
    private String description;

    /**
     * 是否启用
     */
    @Column("enabled")
    private Boolean enabled = true;

    /**
     * 创建时间
     */
    @Column("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Column("update_time")
    private LocalDateTime updateTime;

    /**
     * 过期时间
     */
    @Column("expire_time")
    private LocalDateTime expireTime;

    /**
     * 操作人
     */
    @Column("operator")
    private String operator;

    /**
     * 领域事件列表
     */
    @Column(ignore = true)
    private final List<Object> domainEvents = new ArrayList<>();

    /**
     * 创建新的抑制规则
     */
    public static AlarmSuppression create(String victim, String attacker, String label,
                                        String description, String operator, LocalDateTime expireTime) {
        AlarmSuppression suppression = new AlarmSuppression();
        suppression.victim = victim;
        suppression.attacker = attacker;
        suppression.label = label;
        suppression.description = description;
        suppression.operator = operator;
        suppression.enabled = true;
        suppression.createTime = LocalDateTime.now();
        suppression.updateTime = LocalDateTime.now();
        suppression.expireTime = expireTime;

        // 发布创建事件
        suppression.addDomainEvent(new SuppressionRuleCreatedEvent(
            suppression.getSuppressionId(),
            suppression.getRule(),
            operator
        ));

        log.info("创建抑制规则: victim={}, attacker={}, label={}, operator={}",
                victim, attacker, label, operator);
        return suppression;
    }

    /**
     * 启用规则
     */
    public void enable(String operator) {
        if (Boolean.TRUE.equals(this.enabled)) {
            log.warn("抑制规则已经启用: id={}", this.id);
            return;
        }

        this.enabled = true;
        this.updateTime = LocalDateTime.now();
        this.operator = operator;

        log.info("启用抑制规则: id={}, operator={}", this.id, operator);
    }

    /**
     * 禁用规则
     */
    public void disable(String operator) {
        if (Boolean.FALSE.equals(this.enabled)) {
            log.warn("抑制规则已经禁用: id={}", this.id);
            return;
        }

        this.enabled = false;
        this.updateTime = LocalDateTime.now();
        this.operator = operator;

        log.info("禁用抑制规则: id={}, operator={}", this.id, operator);
    }

    /**
     * 更新规则
     */
    public void update(String victim, String attacker, String label,
                      String description, String operator, LocalDateTime expireTime) {
        SuppressionRule oldRule = getRule();

        this.victim = victim;
        this.attacker = attacker;
        this.label = label;
        this.description = description;
        this.operator = operator;
        this.expireTime = expireTime;
        this.updateTime = LocalDateTime.now();

        // 发布更新事件
        addDomainEvent(new SuppressionRuleUpdatedEvent(
            getSuppressionId(),
            oldRule,
            getRule(),
            operator
        ));

        log.info("更新抑制规则: id={}, operator={}", this.id, operator);
    }

    /**
     * 检查规则是否活跃
     */
    public boolean isActive() {
        return Boolean.TRUE.equals(this.enabled) && !isExpired();
    }

    /**
     * 检查规则是否过期
     */
    public boolean isExpired() {
        return this.expireTime != null && LocalDateTime.now().isAfter(this.expireTime);
    }

    /**
     * 检查是否匹配告警
     */
    public boolean matches(String victimIp, String attackerIp, String alarmLabel) {
        if (!isActive()) {
            return false;
        }

        return matchesPattern(this.victim, victimIp) &&
               matchesPattern(this.attacker, attackerIp) &&
               matchesPattern(this.label, alarmLabel);
    }

    /**
     * 模式匹配
     */
    private boolean matchesPattern(String pattern, String value) {
        if (pattern == null || pattern.isEmpty()) {
            return true; // 空模式匹配所有
        }
        if ("*".equals(pattern)) {
            return true; // 通配符匹配所有
        }
        return pattern.equals(value); // 精确匹配
    }

    /**
     * 获取抑制规则ID值对象
     */
    public SuppressionId getSuppressionId() {
        return this.id != null ? SuppressionId.of(this.id) : null;
    }

    /**
     * 获取抑制规则值对象
     */
    public SuppressionRule getRule() {
        return new SuppressionRule(this.victim, this.attacker, this.label, this.description);
    }

    /**
     * 添加领域事件
     */
    private void addDomainEvent(Object event) {
        this.domainEvents.add(event);
    }

    /**
     * 获取并清空领域事件
     */
    public List<Object> getDomainEvents() {
        List<Object> events = new ArrayList<>(this.domainEvents);
        this.domainEvents.clear();
        return events;
    }
}
