package com.geeksec.nta.alarm.application.command;

import com.geeksec.nta.alarm.domain.valueobject.AlarmId;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 更新告警状态命令
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateAlarmStatusCommand {
    
    /**
     * 告警ID列表
     */
    private List<AlarmId> alarmIds;
    
    /**
     * 新状态
     */
    private String newStatus;
    
    /**
     * 操作人
     */
    private String operator;
    
    /**
     * 操作原因
     */
    private String reason;
    
    /**
     * 是否强制更新（忽略状态转换规则）
     */
    private Boolean forceUpdate;
    
    /**
     * 验证命令参数
     */
    public void validate() {
        if (alarmIds == null || alarmIds.isEmpty()) {
            throw new IllegalArgumentException("告警ID列表不能为空");
        }
        if (newStatus == null || newStatus.trim().isEmpty()) {
            throw new IllegalArgumentException("新状态不能为空");
        }
        if (operator == null || operator.trim().isEmpty()) {
            throw new IllegalArgumentException("操作人不能为空");
        }
        if (alarmIds.size() > 1000) {
            throw new IllegalArgumentException("批量操作的告警数量不能超过1000个");
        }
    }
}
