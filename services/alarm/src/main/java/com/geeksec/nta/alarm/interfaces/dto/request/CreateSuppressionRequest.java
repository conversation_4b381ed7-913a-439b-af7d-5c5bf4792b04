package com.geeksec.nta.alarm.interfaces.dto.request;

import com.geeksec.nta.alarm.application.command.CreateSuppressionCommand;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 创建抑制规则请求
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "创建抑制规则请求")
public class CreateSuppressionRequest {
    
    @Schema(description = "受害者IP", example = "*************", required = true)
    @NotBlank(message = "受害者IP不能为空")
    private String victim;
    
    @Schema(description = "攻击者IP", example = "********", required = true)
    @NotBlank(message = "攻击者IP不能为空")
    private String attacker;
    
    @Schema(description = "告警标签", example = "SQL注入", required = true)
    @NotBlank(message = "告警标签不能为空")
    private String label;
    
    @Schema(description = "抑制规则名称", example = "临时抑制SQL注入告警", required = true)
    @NotBlank(message = "抑制规则名称不能为空")
    private String suppressionName;
    
    @Schema(description = "抑制规则描述", example = "临时抑制来自内网的SQL注入告警")
    private String description;
    
    @Schema(description = "是否启用", example = "true")
    @Builder.Default
    private Boolean enabled = true;
    
    @Schema(description = "过期时间", example = "2024-12-31T23:59:59")
    private LocalDateTime expiryTime;
    
    @Schema(description = "备注", example = "测试期间临时抑制")
    private String note;
    
    /**
     * 转换为创建命令
     * 
     * @param creator 创建者
     * @return 创建命令
     */
    public CreateSuppressionCommand toCommand(String creator) {
        return CreateSuppressionCommand.builder()
                .victim(victim)
                .attacker(attacker)
                .label(label)
                .suppressionName(suppressionName)
                .description(description)
                .enabled(enabled != null ? enabled : true)
                .expiryTime(expiryTime)
                .creator(creator)
                .note(note)
                .build();
    }
}
