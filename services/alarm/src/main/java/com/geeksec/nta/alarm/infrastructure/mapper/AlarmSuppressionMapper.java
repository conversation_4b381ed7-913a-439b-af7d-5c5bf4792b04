package com.geeksec.nta.alarm.infrastructure.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.geeksec.nta.alarm.domain.aggregate.suppression.AlarmSuppression;
import com.mybatisflex.core.BaseMapper;

/**
 * 告警抑制规则数据访问接口
 * 
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
@Mapper
public interface AlarmSuppressionMapper extends BaseMapper<AlarmSuppression> {

    /**
     * 检查告警是否满足抑制规则
     *
     * @param victim 受害者IP
     * @param attacker 攻击者IP
     * @param label 告警标签
     * @return 是否满足抑制规则
     */
    @Select("SELECT COUNT(*) > 0 FROM alarm_suppression WHERE victim = #{victim} AND attacker = #{attacker} AND label = #{label}")
    boolean existsByVictimAndAttackerAndLabel(@Param("victim") String victim, 
                                             @Param("attacker") String attacker, 
                                             @Param("label") String label);

    /**
     * 根据受害者IP查询抑制规则
     * 
     * @param victim 受害者IP
     * @return 抑制规则列表
     */
    @Select("SELECT * FROM alarm_suppression WHERE victim = #{victim}")
    List<AlarmSuppression> selectByVictim(@Param("victim") String victim);

    /**
     * 根据攻击者IP查询抑制规则
     * 
     * @param attacker 攻击者IP
     * @return 抑制规则列表
     */
    @Select("SELECT * FROM alarm_suppression WHERE attacker = #{attacker}")
    List<AlarmSuppression> selectByAttacker(@Param("attacker") String attacker);

    /**
     * 根据告警标签查询抑制规则
     * 
     * @param label 告警标签
     * @return 抑制规则列表
     */
    @Select("SELECT * FROM alarm_suppression WHERE label = #{label}")
    List<AlarmSuppression> selectByLabel(@Param("label") String label);

    /**
     * 根据受害者、攻击者和标签查询抑制规则
     * 
     * @param victim 受害者IP
     * @param attacker 攻击者IP
     * @param label 告警标签
     * @return 抑制规则记录
     */
    @Select("SELECT * FROM alarm_suppression WHERE victim = #{victim} AND attacker = #{attacker} AND label = #{label}")
    AlarmSuppression selectByVictimAndAttackerAndLabel(@Param("victim") String victim,
                                                       @Param("attacker") String attacker,
                                                       @Param("label") String label);

    /**
     * 批量插入抑制规则项
     * 
     * @param suppressionItems 抑制规则项列表
     * @return 插入的记录数
     */
    default int batchInsert(List<AlarmSuppression> suppressionItems) {
        return insertBatch(suppressionItems);
    }

    /**
     * 根据受害者IP删除抑制规则
     *
     * @param victim 受害者IP
     * @return 删除行数
     */
    @Select("DELETE FROM alarm_suppression WHERE victim = #{victim}")
    int deleteByVictim(@Param("victim") String victim);

    /**
     * 根据攻击者IP删除抑制规则
     *
     * @param attacker 攻击者IP
     * @return 删除行数
     */
    @Select("DELETE FROM alarm_suppression WHERE attacker = #{attacker}")
    int deleteByAttacker(@Param("attacker") String attacker);


}
