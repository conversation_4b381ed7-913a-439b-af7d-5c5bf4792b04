package com.geeksec.nta.alarm.infrastructure.repository;

import com.geeksec.nta.alarm.domain.entity.AlarmType;

import java.util.List;

/**
 * 告警类型仓储接口
 *
 * <AUTHOR>
 * @since 3.0.0
 */
public interface AlarmTypeRepository {

    /**
     * 查找所有启用的告警类型
     *
     * @return 告警类型列表
     */
    List<AlarmType> findAllEnabled();

    /**
     * 根据类型代码查找告警类型
     *
     * @param typeCode 类型代码
     * @return 告警类型
     */
    AlarmType findByTypeCode(String typeCode);

    /**
     * 根据ID查找告警类型
     *
     * @param id 告警类型ID
     * @return 告警类型
     */
    AlarmType findById(Integer id);

    /**
     * 根据分类查找告警类型
     *
     * @param category 分类
     * @return 告警类型列表
     */
    List<AlarmType> findByCategory(String category);

    /**
     * 保存告警类型
     *
     * @param alarmType 告警类型
     * @return 保存后的告警类型
     */
    AlarmType save(AlarmType alarmType);

    /**
     * 删除告警类型
     *
     * @param id 告警类型ID
     */
    void deleteById(Integer id);
}