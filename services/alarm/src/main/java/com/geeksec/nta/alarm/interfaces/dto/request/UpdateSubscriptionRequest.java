package com.geeksec.nta.alarm.interfaces.dto.request;

import com.geeksec.nta.alarm.domain.aggregate.subscription.AlarmSubscription.FrequencyType;
import lombok.Data;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.List;

/**
 * 更新订阅请求 DTO
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
public class UpdateSubscriptionRequest {
    
    /**
     * 订阅名称
     */
    @NotBlank(message = "订阅名称不能为空")
    @Size(max = 100, message = "订阅名称长度不能超过100个字符")
    private String subscriptionName;
    
    /**
     * 订阅描述
     */
    @Size(max = 500, message = "订阅描述长度不能超过500个字符")
    private String description;
    
    /**
     * 优先级（1-5）
     */
    private Integer priorityLevel;
    
    /**
     * 匹配规则列表
     */
    @NotEmpty(message = "匹配规则不能为空")
    @Valid
    private List<SubscriptionRuleDto> matchRules;
    
    /**
     * 通知渠道列表
     */
    @NotEmpty(message = "通知渠道不能为空")
    @Valid
    private List<NotificationChannelDto> notificationChannels;
    
    /**
     * 通知频率类型
     */
    @NotNull(message = "通知频率类型不能为空")
    private FrequencyType frequencyType;
    
    /**
     * 频率控制配置
     */
    @Valid
    private FrequencyConfigDto frequencyConfig;
    
    /**
     * 是否启用免打扰
     */
    private Boolean quietHoursEnabled;
    
    /**
     * 免打扰时间配置
     */
    @Valid
    private QuietHoursConfigDto quietHoursConfig;

    /**
     * 转换为更新订阅命令
     *
     * @param subscriptionId 订阅ID
     * @param operator 操作人
     * @return 更新订阅命令
     */
    public com.geeksec.nta.alarm.application.command.UpdateSubscriptionCommand toCommand(String subscriptionId, String operator) {
        return com.geeksec.nta.alarm.application.command.UpdateSubscriptionCommand.builder()
                .subscriptionId(com.geeksec.nta.alarm.domain.valueobject.SubscriptionId.of(subscriptionId))
                .subscriptionName(this.subscriptionName)
                .description(this.description)
                .priorityLevel(this.priorityLevel)
                .matchRules(this.matchRules)
                .notificationChannels(this.notificationChannels)
                .frequencyType(this.frequencyType)
                .frequencyConfig(this.frequencyConfig)
                .quietHoursEnabled(this.quietHoursEnabled)
                .quietHoursConfig(this.quietHoursConfig)
                .operator(operator)
                .build();
    }
}
