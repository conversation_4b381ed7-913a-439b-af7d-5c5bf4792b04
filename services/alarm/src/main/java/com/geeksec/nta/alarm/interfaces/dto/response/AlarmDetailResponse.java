package com.geeksec.nta.alarm.interfaces.dto.response;

import com.geeksec.common.enums.AlarmHandlingStatus;
import com.geeksec.common.enums.AlarmTypeEnum;
import com.geeksec.common.enums.ThreatLevelEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 告警详情响应对象
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlarmDetailResponse implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 告警ID
     */
    private String alarmId;
    
    /**
     * 告警标题
     */
    private String title;
    
    /**
     * 告警描述
     */
    private String description;
    
    /**
     * 告警类型
     */
    private AlarmTypeEnum alarmType;
    
    /**
     * 威胁等级
     */
    private ThreatLevelEnum threatLevel;
    
    /**
     * 处理状态
     */
    private AlarmHandlingStatus status;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 告警来源
     */
    private AlarmSourceInfo source;
    
    /**
     * 攻击者信息
     */
    private AlarmAttackerInfo attacker;
    
    /**
     * 受害者信息
     */
    private AlarmVictimInfo victim;
    
    /**
     * 告警原因
     */
    private AlarmReasonInfo reason;
    
    /**
     * 告警目标
     */
    private List<AlarmTargetInfo> targets;
    
    /**
     * 扩展属性
     */
    private Map<String, Object> attributes;
    
    /**
     * 处理历史
     */
    private List<AlarmHandlingHistory> handlingHistory;
    
    /**
     * 告警来源信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AlarmSourceInfo implements Serializable {
        private String sourceType;
        private String sourceId;
        private String sourceName;
        private Map<String, Object> sourceData;
    }
    
    /**
     * 攻击者信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AlarmAttackerInfo implements Serializable {
        private String ip;
        private String hostname;
        private String location;
        private Map<String, Object> attributes;
    }
    
    /**
     * 受害者信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AlarmVictimInfo implements Serializable {
        private String ip;
        private String hostname;
        private String location;
        private Map<String, Object> attributes;
    }
    
    /**
     * 告警原因信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AlarmReasonInfo implements Serializable {
        private String reasonType;
        private String reasonCode;
        private String reasonMessage;
        private Map<String, Object> reasonData;
    }
    
    /**
     * 告警目标信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AlarmTargetInfo implements Serializable {
        private String targetType;
        private String targetId;
        private String targetName;
        private Map<String, Object> targetData;
    }
    
    /**
     * 告警处理历史
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AlarmHandlingHistory implements Serializable {
        private String operationType;
        private String operatorId;
        private String operatorName;
        private String comment;
        private LocalDateTime operationTime;
        private AlarmHandlingStatus fromStatus;
        private AlarmHandlingStatus toStatus;
    }
}