package com.geeksec.nta.alarm.infrastructure.converter;

import com.geeksec.nta.alarm.domain.aggregate.suppression.AlarmSuppression;
import com.geeksec.nta.alarm.interfaces.dto.response.AlarmSuppressionResponse;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 告警抑制转换器
 * 用于在不同层之间转换告警抑制对象
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Component
public class AlarmSuppressionConverter {

    /**
     * 将 AlarmSuppression 实体转换为 AlarmSuppressionResponse
     *
     * @param suppression 告警抑制实体
     * @return 告警抑制响应对象
     */
    public AlarmSuppressionResponse toResponse(AlarmSuppression suppression) {
        if (suppression == null) {
            return null;
        }

        return AlarmSuppressionResponse.builder()
                .suppressionId(suppression.getId().toString())
                .ruleName(suppression.getSuppressionName())
                .description(suppression.getDescription())
                .suppressionType("IP_BASED") // 基于我们的新结构，这是IP基础的抑制
                .enabled(suppression.getEnabled())
                .priority(1) // 默认优先级
                .conditions(List.of()) // 暂时为空，可以后续扩展
                .action("SUPPRESS") // 默认动作
                .durationMinutes(null) // 基于过期时间计算
                .effectiveTimeRanges(List.of()) // 暂时为空
                .attributes(Map.of(
                    "victim", suppression.getVictim(),
                    "attacker", suppression.getAttacker(),
                    "label", suppression.getLabel()
                ))
                .createdBy(suppression.getCreator())
                .createTime(suppression.getCreateTime())
                .updatedBy(suppression.getOperator())
                .updateTime(suppression.getUpdateTime())
                .lastTriggeredTime(suppression.getLastMatchTime())
                .triggerCount(suppression.getMatchCount())
                .suppressedAlarmCount(suppression.getMatchCount()) // 使用匹配次数作为抑制次数
                .build();
    }

    /**
     * 批量转换为响应对象列表
     *
     * @param suppressions 告警抑制实体列表
     * @return 告警抑制响应对象列表
     */
    public List<AlarmSuppressionResponse> toResponseList(List<AlarmSuppression> suppressions) {
        if (suppressions == null) {
            return null;
        }
        return suppressions.stream()
                .map(this::toResponse)
                .collect(Collectors.toList());
    }
}