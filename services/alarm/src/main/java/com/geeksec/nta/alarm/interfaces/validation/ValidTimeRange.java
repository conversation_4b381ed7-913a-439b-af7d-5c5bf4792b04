package com.geeksec.nta.alarm.interfaces.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.*;

/**
 * 时间范围验证注解
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = ValidTimeRangeValidator.class)
@Documented
public @interface ValidTimeRange {
    
    String message() default "时间范围无效，开始时间不能晚于结束时间";
    
    Class<?>[] groups() default {};
    
    Class<? extends Payload>[] payload() default {};
}