package com.geeksec.nta.alarm.application.service.integration;

import com.geeksec.nta.alarm.domain.aggregate.Alarm;
import com.geeksec.nta.alarm.domain.aggregate.AlarmSubscription;

import java.util.List;

/**
 * 告警通知应用服务
 * 负责处理告警通知相关的业务逻辑
 *
 * 注意：实际的通知发送由 flink-jobs/alarm-notification 模块负责
 *
 * <AUTHOR>
 * @since 3.0.0
 */
public interface AlarmNotificationService {
    
    /**
     * 处理告警通知
     * 
     * @param alarm 告警
     * @return 通知结果
     */
    NotificationResult processAlarmNotification(Alarm alarm);
    
    /**
     * 批量处理告警通知
     * 
     * @param alarms 告警列表
     * @return 批量通知结果
     */
    BatchNotificationResult batchProcessAlarmNotifications(List<Alarm> alarms);
    
    /**
     * 发送测试通知
     * 
     * @param subscription 订阅
     * @param testMessage 测试消息
     * @return 测试结果
     */
    TestNotificationResult sendTestNotification(AlarmSubscription subscription, String testMessage);
    
    /**
     * 重新发送失败的通知
     * 
     * @param notificationId 通知ID
     * @return 重发结果
     */
    ResendResult resendFailedNotification(String notificationId);
    
    /**
     * 获取通知历史
     * 
     * @param userId 用户ID
     * @param page 页码
     * @param size 页大小
     * @return 通知历史
     */
    NotificationHistoryResult getNotificationHistory(String userId, int page, int size);
    
    /**
     * 获取通知统计
     * 
     * @param userId 用户ID
     * @param days 统计天数
     * @return 通知统计
     */
    NotificationStatistics getNotificationStatistics(String userId, int days);
    
    /**
     * 通知结果
     */
    record NotificationResult(
        String notificationId,
        boolean success,
        int matchedSubscriptions,
        int sentNotifications,
        int failedNotifications,
        List<NotificationDetail> details,
        String message
    ) {}
    
    /**
     * 批量通知结果
     */
    record BatchNotificationResult(
        int processedAlarms,
        int totalNotifications,
        int successfulNotifications,
        int failedNotifications,
        List<String> errors,
        String summary
    ) {}
    
    /**
     * 测试通知结果
     */
    record TestNotificationResult(
        boolean success,
        List<ChannelTestResult> channelResults,
        String message
    ) {}
    
    /**
     * 重发结果
     */
    record ResendResult(
        boolean success,
        String originalNotificationId,
        String newNotificationId,
        String message
    ) {}
    
    /**
     * 通知历史结果
     */
    record NotificationHistoryResult(
        List<NotificationHistoryItem> items,
        long total,
        int page,
        int size
    ) {}
    
    /**
     * 通知统计
     */
    record NotificationStatistics(
        long totalNotifications,
        long successfulNotifications,
        long failedNotifications,
        double successRate,
        List<ChannelStatistics> channelStats,
        List<DailyStatistics> dailyStats
    ) {}
    
    /**
     * 通知详情
     */
    record NotificationDetail(
        String subscriptionId,
        String subscriptionName,
        String channelType,
        boolean success,
        String message,
        long timestamp
    ) {}
    
    /**
     * 渠道测试结果
     */
    record ChannelTestResult(
        String channelType,
        String channelName,
        boolean success,
        String message,
        long responseTime
    ) {}
    
    /**
     * 通知历史项
     */
    record NotificationHistoryItem(
        String notificationId,
        String alarmId,
        String subscriptionName,
        String channelType,
        boolean success,
        String message,
        long timestamp
    ) {}
    
    /**
     * 渠道统计
     */
    record ChannelStatistics(
        String channelType,
        long totalCount,
        long successCount,
        long failedCount,
        double successRate
    ) {}
    
    /**
     * 日统计
     */
    record DailyStatistics(
        String date,
        long totalCount,
        long successCount,
        long failedCount
    ) {}
}
