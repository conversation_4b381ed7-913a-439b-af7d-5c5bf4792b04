package com.geeksec.nta.alarm.interfaces.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.*;

/**
 * IP地址验证注解
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Target({ElementType.FIELD, ElementType.PARAMETER, ElementType.TYPE_USE})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = ValidIpAddressValidator.class)
@Documented
public @interface ValidIpAddress {
    
    String message() default "无效的IP地址格式";
    
    Class<?>[] groups() default {};
    
    Class<? extends Payload>[] payload() default {};
}