package com.geeksec.nta.alarm.interfaces.dto.response;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

import com.geeksec.common.enums.AlarmHandlingStatus;
import com.geeksec.common.enums.AlarmTypeEnum;
import com.geeksec.common.enums.ThreatLevelEnum;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 告警列表响应对象
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlarmListResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 告警ID
     */
    private String alarmId;

    /**
     * 告警标题
     */
    private String title;

    /**
     * 告警类型
     */
    private AlarmTypeEnum alarmType;

    /**
     * 威胁等级
     */
    private ThreatLevelEnum threatLevel;

    /**
     * 处理状态
     */
    private AlarmHandlingStatus status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 攻击者IP
     */
    private String attackerIp;

    /**
     * 受害者IP
     */
    private String victimIp;

    /**
     * 告警来源
     */
    private String sourceType;

    /**
     * 告警原因代码
     */
    private String reasonCode;

    /**
     * 告警原因描述
     */
    private String reasonMessage;

    /**
     * 处理人ID
     */
    private String handlerId;

    /**
     * 处理人姓名
     */
    private String handlerName;

    /**
     * 处理时间
     */
    private LocalDateTime handleTime;

    /**
     * 扩展属性
     */
    private Map<String, Object> attributes;

    /**
     * 告警评分
     */
    private Double score;

    /**
     * 是否已读
     */
    @Builder.Default
    private Boolean isRead = false;

    /**
     * 是否已处理
     */
    public Boolean isHandled() {
        return status != null && status == AlarmHandlingStatus.HANDLED;
    }

    /**
     * 获取威胁等级颜色
     */
    public String getThreatLevelColor() {
        if (threatLevel == null) {
            return "#666666";
        }
        return switch (threatLevel) {
            case CRITICAL -> "#ff4d4f";
            case HIGH -> "#ff7a45";
            case MEDIUM -> "#ffa940";
            case LOW -> "#52c41a";
            case NONE -> "#1890ff";
        };
    }

    /**
     * 获取状态颜色
     */
    public String getStatusColor() {
        if (status == null) {
            return "#666666";
        }
        return switch (status) {
            case UNHANDLED -> "#ff4d4f";
            case UNCONFIRMED -> "#1890ff";
            case CONFIRMED -> "#ffa940";
            case HANDLED -> "#52c41a";
            case FALSE_POSITIVE -> "#d9d9d9";
        };
    }
}