package com.geeksec.nta.alarm.domain.valueobject;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

/**
 * 抑制规则值对象
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Getter
@ToString
@EqualsAndHashCode
public final class SuppressionRule {
    
    /**
     * 通配符常量
     */
    public static final String WILDCARD = "*";
    
    /**
     * 受害者IP
     */
    private final String victim;
    
    /**
     * 攻击者IP
     */
    private final String attacker;
    
    /**
     * 告警标签
     */
    private final String label;
    
    /**
     * 规则描述
     */
    private final String description;
    
    public SuppressionRule(String victim, String attacker, String label, String description) {
        // 验证规则有效性
        validateRule(victim, attacker, label);
        
        this.victim = victim;
        this.attacker = attacker;
        this.label = label;
        this.description = description;
    }
    
    /**
     * 验证规则有效性
     */
    private static void validateRule(String victim, String attacker, String label) {
        if ((victim == null || victim.trim().isEmpty()) &&
            (attacker == null || attacker.trim().isEmpty()) &&
            (label == null || label.trim().isEmpty())) {
            throw new IllegalArgumentException("抑制规则至少需要指定一个条件");
        }
    }
    
    /**
     * 检查是否匹配告警
     */
    public boolean matches(String victimIp, String attackerIp, String alarmLabel) {
        return matchesPattern(this.victim, victimIp) &&
               matchesPattern(this.attacker, attackerIp) &&
               matchesPattern(this.label, alarmLabel);
    }
    
    /**
     * 模式匹配
     */
    private boolean matchesPattern(String pattern, String value) {
        if (pattern == null || pattern.trim().isEmpty()) {
            // 空模式匹配所有
            return true;
        }
        if (WILDCARD.equals(pattern)) {
            // 通配符匹配所有
            return true;
        }
        // 精确匹配
        return pattern.equals(value);
    }
    
    /**
     * 检查是否为通配符规则
     */
    public boolean isWildcardRule() {
        return WILDCARD.equals(victim) || WILDCARD.equals(attacker) || WILDCARD.equals(label);
    }
}
