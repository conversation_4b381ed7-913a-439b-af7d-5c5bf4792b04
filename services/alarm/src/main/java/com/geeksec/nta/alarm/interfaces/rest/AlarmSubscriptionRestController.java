package com.geeksec.nta.alarm.interfaces.rest;

import com.geeksec.nta.alarm.application.service.query.AlarmSubscriptionQueryService;
import com.geeksec.nta.alarm.application.service.command.AlarmSubscriptionCommandService;
import com.geeksec.common.dto.ApiResponse;
import com.geeksec.common.entity.PageResultVo;

import com.geeksec.nta.alarm.interfaces.dto.request.CreateSubscriptionRequest;
import com.geeksec.nta.alarm.interfaces.dto.request.UpdateSubscriptionRequest;
import com.geeksec.nta.alarm.interfaces.dto.request.SubscriptionQueryRequest;
import com.geeksec.nta.alarm.interfaces.dto.response.AlarmSubscriptionResponse;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import cn.dev33.satoken.stp.StpUtil;
import org.springframework.web.bind.annotation.*;

/**
 * 告警订阅REST控制器
 * 负责告警订阅资源的RESTful API
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/subscriptions")
@RequiredArgsConstructor
@Tag(name = "告警订阅", description = "告警订阅管理相关接口")
public class AlarmSubscriptionRestController {
    
    private final AlarmSubscriptionQueryService subscriptionQueryService;
    private final AlarmSubscriptionCommandService subscriptionCommandService;
    
    // ==================== 订阅查询操作 ====================
    
    /**
     * 查询订阅列表
     */
    @GetMapping
    @Operation(summary = "查询订阅列表", description = "分页查询用户的告警订阅列表")
    public ApiResponse<PageResultVo<AlarmSubscriptionResponse>> getSubscriptions(
            @Valid @ModelAttribute SubscriptionQueryRequest request) {
        String currentUserId = StpUtil.getLoginIdAsString();
        
        log.info("查询订阅列表: request={}, userId={}", request, currentUserId);
        
        var query = request.toQuery(currentUserId);
        var result = subscriptionQueryService.querySubscriptions(query);

        return ApiResponse.success("查询成功", result);
    }
    
    /**
     * 查询订阅详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "查询订阅详情", description = "根据订阅ID查询详细信息")
    public ApiResponse<AlarmSubscriptionResponse> getSubscription(
            @Parameter(description = "订阅ID") @PathVariable String id) {
        String currentUserId = StpUtil.getLoginIdAsString();
        
        log.info("查询订阅详情: id={}, userId={}", id, currentUserId);
        
        var subscriptionId = com.geeksec.nta.alarm.domain.valueobject.SubscriptionId.of(id);
        var userId = com.geeksec.nta.alarm.domain.valueobject.UserId.of(currentUserId);
        
        var subscription = subscriptionQueryService.getSubscription(subscriptionId, userId);
        
        if (subscription.isPresent()) {
            return ApiResponse.success("查询成功", subscription.get());
        } else {
            return ApiResponse.notFound("订阅");
        }
    }
    
    // ==================== 订阅管理操作 ====================
    
    /**
     * 创建订阅
     */
    @PostMapping
    @Operation(summary = "创建告警订阅", description = "创建新的告警订阅规则")
    @ResponseStatus(HttpStatus.CREATED)
    public ApiResponse<Long> createSubscription(
            @Valid @RequestBody CreateSubscriptionRequest request) {
        String currentUserId = StpUtil.getLoginIdAsString();
        
        log.info("创建订阅: request={}, userId={}", request, currentUserId);
        
        var command = request.toCommand(currentUserId);
        var subscriptionId = subscriptionCommandService.createSubscription(command);
        
        return ApiResponse.success("订阅创建成功", subscriptionId.getValue());
    }
    
    /**
     * 更新订阅
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新告警订阅", description = "完整更新告警订阅信息")
    public ApiResponse<String> updateSubscription(
            @Parameter(description = "订阅ID") @PathVariable String id,
            @Valid @RequestBody UpdateSubscriptionRequest request) {
        String currentUserId = StpUtil.getLoginIdAsString();
        
        log.info("更新订阅: id={}, request={}, userId={}", id, request, currentUserId);
        
        var command = request.toCommand(id, currentUserId);
        boolean success = subscriptionCommandService.updateSubscription(command);
        
        if (success) {
            return ApiResponse.success("订阅更新成功");
        } else {
            return ApiResponse.businessError("订阅更新失败");
        }
    }
    
    /**
     * 删除订阅
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除告警订阅", description = "删除指定的告警订阅")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public ApiResponse<String> deleteSubscription(
            @Parameter(description = "订阅ID") @PathVariable String id) {
        String currentUserId = StpUtil.getLoginIdAsString();
        
        log.info("删除订阅: id={}, userId={}", id, currentUserId);
        
        var command = new com.geeksec.nta.alarm.application.command.DeleteSubscriptionCommand(
                com.geeksec.nta.alarm.domain.valueobject.SubscriptionId.of(id),
                currentUserId,
                "用户删除"
        );
        
        boolean success = subscriptionCommandService.deleteSubscription(command);
        
        if (success) {
            return ApiResponse.success("订阅删除成功");
        } else {
            return ApiResponse.businessError("订阅删除失败");
        }
    }
    
    // ==================== 订阅状态管理 ====================
    
    /**
     * 启用/禁用订阅
     */
    @PatchMapping("/{id}/status")
    @Operation(summary = "启用/禁用订阅", description = "切换订阅的启用状态")
    public ApiResponse<String> toggleSubscription(
            @Parameter(description = "订阅ID") @PathVariable String id,
            @Parameter(description = "是否启用") @RequestParam Boolean enabled) {
        String currentUserId = StpUtil.getLoginIdAsString();
        
        log.info("切换订阅状态: id={}, enabled={}, userId={}", id, enabled, currentUserId);
        
        var command = new com.geeksec.nta.alarm.application.command.ToggleSubscriptionCommand(
                com.geeksec.nta.alarm.domain.valueobject.SubscriptionId.of(id),
                enabled,
                currentUserId
        );
        
        boolean success = subscriptionCommandService.toggleSubscription(command);
        
        if (success) {
            String message = enabled ? "订阅已启用" : "订阅已禁用";
            return ApiResponse.success(message);
        } else {
            return ApiResponse.businessError("状态切换失败");
        }
    }
    
    /**
     * 复制订阅
     */
    @PostMapping("/{id}/copy")
    @Operation(summary = "复制订阅", description = "复制现有订阅创建新的订阅")
    @ResponseStatus(HttpStatus.CREATED)
    public ApiResponse<Long> copySubscription(
            @Parameter(description = "源订阅ID") @PathVariable String id,
            @Parameter(description = "新订阅名称") @RequestParam String newName) {
        String currentUserId = StpUtil.getLoginIdAsString();
        
        log.info("复制订阅: sourceId={}, newName={}, userId={}", id, newName, currentUserId);
        
        var sourceSubscriptionId = com.geeksec.nta.alarm.domain.valueobject.SubscriptionId.of(id);
        var newSubscriptionId = subscriptionCommandService.copySubscription(
                sourceSubscriptionId, newName, currentUserId);
        
        return ApiResponse.success("订阅复制成功", newSubscriptionId.getValue());
    }
}
