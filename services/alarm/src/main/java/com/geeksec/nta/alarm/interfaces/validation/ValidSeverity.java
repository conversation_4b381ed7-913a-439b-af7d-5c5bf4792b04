package com.geeksec.nta.alarm.interfaces.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.*;

/**
 * 告警严重程度验证注解
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Target({ElementType.FIELD, ElementType.PARAMETER, ElementType.TYPE_USE})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = ValidSeverityValidator.class)
@Documented
public @interface ValidSeverity {
    
    String message() default "无效的告警严重程度";
    
    Class<?>[] groups() default {};
    
    Class<? extends Payload>[] payload() default {};
}