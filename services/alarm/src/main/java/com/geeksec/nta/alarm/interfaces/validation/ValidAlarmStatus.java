package com.geeksec.nta.alarm.interfaces.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.*;

/**
 * 告警状态验证注解
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Target({ElementType.FIELD, ElementType.PARAMETER, ElementType.TYPE_USE})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = ValidAlarmStatusValidator.class)
@Documented
public @interface ValidAlarmStatus {
    
    String message() default "无效的告警状态";
    
    Class<?>[] groups() default {};
    
    Class<? extends Payload>[] payload() default {};
}