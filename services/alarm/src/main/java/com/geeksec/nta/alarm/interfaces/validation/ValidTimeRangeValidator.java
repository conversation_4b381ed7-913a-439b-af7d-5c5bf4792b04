package com.geeksec.nta.alarm.interfaces.validation;

import com.geeksec.common.dto.BaseQueryRequest;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

/**
 * 时间范围验证器
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public class ValidTimeRangeValidator implements ConstraintValidator<ValidTimeRange, BaseQueryRequest> {
    
    @Override
    public void initialize(ValidTimeRange constraintAnnotation) {
        // 初始化方法，可以为空
    }
    
    @Override
    public boolean isValid(BaseQueryRequest value, ConstraintValidatorContext context) {
        if (value == null) {
            return true;
        }
        
        // 检查创建时间范围
        if (value.getCreateTimeStart() != null && value.getCreateTimeEnd() != null) {
            if (value.getCreateTimeStart().isAfter(value.getCreateTimeEnd())) {
                return false;
            }
        }
        
        // 检查更新时间范围
        if (value.getUpdateTimeStart() != null && value.getUpdateTimeEnd() != null) {
            if (value.getUpdateTimeStart().isAfter(value.getUpdateTimeEnd())) {
                return false;
            }
        }
        
        return true;
    }
}