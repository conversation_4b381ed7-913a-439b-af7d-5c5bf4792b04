package com.geeksec.nta.alarm.application.command;

import com.geeksec.nta.alarm.domain.valueobject.AlarmId;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 删除告警命令
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeleteAlarmCommand {
    
    /**
     * 告警ID
     */
    private AlarmId alarmId;
    
    /**
     * 操作人
     */
    private String operator;
    
    /**
     * 删除原因
     */
    private String reason;
    
    /**
     * 验证命令参数
     */
    public void validate() {
        if (alarmId == null) {
            throw new IllegalArgumentException("告警ID不能为空");
        }
        if (operator == null || operator.trim().isEmpty()) {
            throw new IllegalArgumentException("操作人不能为空");
        }
    }
}