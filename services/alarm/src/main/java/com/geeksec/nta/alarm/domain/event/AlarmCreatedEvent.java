package com.geeksec.nta.alarm.domain.event;

import lombok.Data;
import lombok.RequiredArgsConstructor;

import java.time.LocalDateTime;

/**
 * 告警创建事件
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@RequiredArgsConstructor
public class AlarmCreatedEvent {
    
    /**
     * 告警ID
     */
    private final String alarmId;
    
    /**
     * 告警类型
     */
    private final String alarmType;
    
    /**
     * 告警分数
     */
    private final int score;
    
    /**
     * 事件发生时间
     */
    private final LocalDateTime occurredAt = LocalDateTime.now();
}
