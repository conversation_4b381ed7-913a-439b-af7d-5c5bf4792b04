package com.geeksec.nta.alarm.application.command;

import com.geeksec.nta.alarm.domain.valueobject.SuppressionId;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 更新抑制规则命令
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateSuppressionCommand {
    
    /**
     * 抑制规则ID
     */
    private SuppressionId suppressionId;
    
    /**
     * 受害者IP
     */
    private String victim;
    
    /**
     * 攻击者IP
     */
    private String attacker;
    
    /**
     * 告警标签
     */
    private String label;
    
    /**
     * 抑制规则名称
     */
    private String suppressionName;
    
    /**
     * 抑制规则描述
     */
    private String description;
    
    /**
     * 是否启用
     */
    private Boolean enabled;
    
    /**
     * 过期时间（可选，null表示永不过期）
     */
    private LocalDateTime expiryTime;
    
    /**
     * 操作人
     */
    private String operator;
    
    /**
     * 备注
     */
    private String note;
    
    /**
     * 验证命令参数
     */
    public void validate() {
        if (suppressionId == null) {
            throw new IllegalArgumentException("抑制规则ID不能为空");
        }
        if (operator == null || operator.trim().isEmpty()) {
            throw new IllegalArgumentException("操作人不能为空");
        }
        
        // 如果提供了新的IP，需要验证格式
        if (victim != null && !isValidIpAddress(victim)) {
            throw new IllegalArgumentException("受害者IP格式不正确");
        }
        if (attacker != null && !isValidIpAddress(attacker)) {
            throw new IllegalArgumentException("攻击者IP格式不正确");
        }
        
        // 验证过期时间
        if (expiryTime != null && expiryTime.isBefore(LocalDateTime.now())) {
            throw new IllegalArgumentException("过期时间不能早于当前时间");
        }
        
        // 验证至少有一个字段需要更新
        if (victim == null && attacker == null && label == null && 
            suppressionName == null && description == null && 
            enabled == null && expiryTime == null && note == null) {
            throw new IllegalArgumentException("至少需要更新一个字段");
        }
    }
    
    /**
     * 简单的IP地址格式验证
     */
    private boolean isValidIpAddress(String ip) {
        if (ip == null || ip.trim().isEmpty()) {
            return false;
        }
        
        // 支持通配符
        if ("*".equals(ip.trim())) {
            return true;
        }
        
        // 简单的IPv4格式验证
        String[] parts = ip.split("\\.");
        if (parts.length != 4) {
            return false;
        }
        
        for (String part : parts) {
            if ("*".equals(part)) {
                continue;
            }
            try {
                int num = Integer.parseInt(part);
                if (num < 0 || num > 255) {
                    return false;
                }
            } catch (NumberFormatException e) {
                return false;
            }
        }
        
        return true;
    }
}
