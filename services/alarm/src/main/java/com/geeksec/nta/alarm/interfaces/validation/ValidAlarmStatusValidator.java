package com.geeksec.nta.alarm.interfaces.validation;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.Set;

/**
 * 告警状态验证器
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public class ValidAlarmStatusValidator implements ConstraintValidator<ValidAlarmStatus, String> {
    
    private static final Set<String> VALID_STATUSES = Set.of(
        "NEW", "CONFIRMED", "IN_PROGRESS", "RESOLVED", "CLOSED", "IGNORED"
    );
    
    @Override
    public void initialize(ValidAlarmStatus constraintAnnotation) {
        // 初始化方法，可以为空
    }
    
    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (value == null) {
            return true; // null值由@NotNull等其他注解处理
        }
        return VALID_STATUSES.contains(value.toUpperCase());
    }
}