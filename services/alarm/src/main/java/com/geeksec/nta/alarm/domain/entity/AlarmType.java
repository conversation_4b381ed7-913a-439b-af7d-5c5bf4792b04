package com.geeksec.nta.alarm.domain.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 告警类型实体
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table("alarm_type_config")
public class AlarmType implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 告警类型ID
     */
    @Id(keyType = KeyType.Auto)
    @Column("id")
    private Integer id;

    /**
     * 告警类型代码
     */
    @Column("type_code")
    private String typeCode;

    /**
     * 告警类型名称
     */
    @Column("type_name")
    private String typeName;

    /**
     * 告警类型描述
     */
    @Column("description")
    private String description;

    /**
     * 告警类型分类
     */
    @Column("category")
    private String category;

    /**
     * 默认威胁等级
     */
    @Column("default_threat_level")
    private String defaultThreatLevel;

    /**
     * 是否启用
     */
    @Column("enabled")
    private Boolean enabled = true;

    /**
     * 排序序号
     */
    @Column("sort_order")
    private Integer sortOrder;

    /**
     * 创建时间
     */
    @Column("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Column("update_time")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @Column("created_by")
    private String createdBy;

    /**
     * 更新人
     */
    @Column("updated_by")
    private String updatedBy;
}