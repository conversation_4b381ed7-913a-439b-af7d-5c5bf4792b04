package com.geeksec.nta.alarm.interfaces.dto.subscription;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 订阅规则 DTO
 * 用于定义告警订阅的匹配规则
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SubscriptionRuleDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 规则类型（ALARM_TYPE, THREAT_LEVEL, SOURCE_IP, TARGET_IP, etc.）
     */
    private String ruleType;

    /**
     * 操作符（EQUALS, CONTAINS, IN, GREATER_THAN, LESS_THAN, etc.）
     */
    private String operator;

    /**
     * 匹配值列表
     */
    private List<String> values;

    /**
     * 是否启用
     */
    @Builder.Default
    private Boolean enabled = true;

    /**
     * 规则描述
     */
    private String description;

    /**
     * 规则优先级
     */
    @Builder.Default
    private Integer priority = 0;

    /**
     * 规则类型枚举
     */
    public enum RuleType {
        /** 告警类型 */
        ALARM_TYPE,
        /** 威胁等级 */
        THREAT_LEVEL,
        /** 源IP */
        SOURCE_IP,
        /** 目标IP */
        TARGET_IP,
        /** 源端口 */
        SOURCE_PORT,
        /** 目标端口 */
        TARGET_PORT,
        /** 协议 */
        PROTOCOL,
        /** 告警分数 */
        ALARM_SCORE,
        /** 自定义字段 */
        CUSTOM_FIELD
    }

    /**
     * 操作符枚举
     */
    public enum Operator {
        /** 等于 */
        EQUALS,
        /** 不等于 */
        NOT_EQUALS,
        /** 包含 */
        CONTAINS,
        /** 不包含 */
        NOT_CONTAINS,
        /** 在列表中 */
        IN,
        /** 不在列表中 */
        NOT_IN,
        /** 大于 */
        GREATER_THAN,
        /** 大于等于 */
        GREATER_THAN_OR_EQUAL,
        /** 小于 */
        LESS_THAN,
        /** 小于等于 */
        LESS_THAN_OR_EQUAL,
        /** 正则匹配 */
        REGEX,
        /** 范围内 */
        BETWEEN
    }

    /**
     * 验证规则是否有效
     *
     * @return 是否有效
     */
    public boolean isValid() {
        if (ruleType == null || ruleType.trim().isEmpty()) {
            return false;
        }
        if (operator == null || operator.trim().isEmpty()) {
            return false;
        }
        if (values == null || values.isEmpty()) {
            return false;
        }
        return true;
    }

    /**
     * 是否为数值比较操作
     *
     * @return 是否为数值比较
     */
    public boolean isNumericComparison() {
        return Operator.GREATER_THAN.name().equals(operator) ||
               Operator.GREATER_THAN_OR_EQUAL.name().equals(operator) ||
               Operator.LESS_THAN.name().equals(operator) ||
               Operator.LESS_THAN_OR_EQUAL.name().equals(operator) ||
               Operator.BETWEEN.name().equals(operator);
    }

    /**
     * 是否为列表操作
     *
     * @return 是否为列表操作
     */
    public boolean isListOperation() {
        return Operator.IN.name().equals(operator) ||
               Operator.NOT_IN.name().equals(operator);
    }

    /**
     * 是否为字符串操作
     *
     * @return 是否为字符串操作
     */
    public boolean isStringOperation() {
        return Operator.CONTAINS.name().equals(operator) ||
               Operator.NOT_CONTAINS.name().equals(operator) ||
               Operator.REGEX.name().equals(operator);
    }
}