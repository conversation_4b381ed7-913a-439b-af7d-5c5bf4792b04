package com.geeksec.nta.alarm.interfaces.dto.request;

import java.util.List;
import java.util.stream.Collectors;

import com.geeksec.common.enums.AlarmHandlingStatus;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 告警状态更新请求
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "告警状态更新请求")
public class AlarmStatusUpdateRequest {

        /**
         * 告警ID列表
         */
        @NotEmpty(message = "告警ID列表不能为空")
        @Schema(description = "告警ID列表", example = "[\"alarm-001\", \"alarm-002\"]")
        private List<String> alarmIds;

        /**
         * 目标状态
         */
        @NotNull(message = "目标状态不能为空")
        @Schema(description = "目标状态", example = "CONFIRMED")
        private AlarmHandlingStatus targetStatus;

        /**
         * 处理人
         */
        @Schema(description = "处理人", example = "admin")
        private String handler;

        /**
         * 处理备注
         */
        @Schema(description = "处理备注", example = "已确认为真实告警")
        private String note;

        /**
         * 转换为单个命令
         * 
         * @param alarmIds 告警ID列表
         * @return 更新命令
         */
        public com.geeksec.nta.alarm.application.command.UpdateAlarmStatusCommand toCommand(List<String> alarmIds) {
                return com.geeksec.nta.alarm.application.command.UpdateAlarmStatusCommand.builder()
                                .alarmIds(alarmIds.stream()
                                                .map(com.geeksec.nta.alarm.domain.valueobject.AlarmId::of)
                                                .collect(Collectors.toList()))
                                .newStatus(targetStatus.name())
                                .operator(handler)
                                .reason(note)
                                .build();
        }

        /**
         * 转换为批量命令列表
         * 
         * @return 批量更新命令列表
         */
        public List<com.geeksec.nta.alarm.application.command.UpdateAlarmStatusCommand> toCommands() {
                return alarmIds.stream()
                                .map(alarmId -> com.geeksec.nta.alarm.application.command.UpdateAlarmStatusCommand.builder()
                                                .alarmIds(List.of(com.geeksec.nta.alarm.domain.valueobject.AlarmId.of(alarmId)))
                                                .newStatus(targetStatus.name())
                                                .operator(handler)
                                                .reason(note)
                                                .build())
                                .collect(Collectors.toList());
        }
}
