package com.geeksec.nta.alarm.interfaces.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 告警抑制响应对象
 * 用于返回告警抑制规则信息
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlarmSuppressionResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 抑制规则ID
     */
    private String suppressionId;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 规则描述
     */
    private String description;

    /**
     * 抑制类型
     */
    private String suppressionType;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 优先级
     */
    private Integer priority;

    /**
     * 匹配条件
     */
    private List<SuppressionCondition> conditions;

    /**
     * 抑制动作
     */
    private String action;

    /**
     * 抑制时长（分钟）
     */
    private Integer durationMinutes;

    /**
     * 生效时间段
     */
    private List<TimeRange> effectiveTimeRanges;

    /**
     * 扩展属性
     */
    private Map<String, Object> attributes;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 最后触发时间
     */
    private LocalDateTime lastTriggeredTime;

    /**
     * 触发次数
     */
    private Integer triggerCount;

    /**
     * 抑制的告警数量
     */
    private Integer suppressedAlarmCount;

    /**
     * 抑制条件内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SuppressionCondition implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        /**
         * 字段名
         */
        private String fieldName;
        
        /**
         * 操作符
         */
        private String operator;
        
        /**
         * 匹配值
         */
        private List<String> values;
        
        /**
         * 是否启用
         */
        private Boolean enabled;
    }

    /**
     * 时间范围内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TimeRange implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        /**
         * 开始时间（HH:mm）
         */
        private String startTime;
        
        /**
         * 结束时间（HH:mm）
         */
        private String endTime;
        
        /**
         * 生效的星期几（1-7）
         */
        private List<Integer> daysOfWeek;
    }

    /**
     * 获取抑制类型显示名称
     *
     * @return 显示名称
     */
    public String getSuppressionTypeDisplay() {
        if (suppressionType == null) {
            return "未知";
        }
        switch (suppressionType.toUpperCase()) {
            case "TIME_BASED":
                return "基于时间";
            case "CONDITION_BASED":
                return "基于条件";
            case "FREQUENCY_BASED":
                return "基于频率";
            case "DEPENDENCY_BASED":
                return "基于依赖";
            default:
                return suppressionType;
        }
    }

    /**
     * 获取动作显示名称
     *
     * @return 显示名称
     */
    public String getActionDisplay() {
        if (action == null) {
            return "未知";
        }
        switch (action.toUpperCase()) {
            case "SUPPRESS":
                return "抑制";
            case "DELAY":
                return "延迟";
            case "REDUCE_PRIORITY":
                return "降低优先级";
            case "MERGE":
                return "合并";
            default:
                return action;
        }
    }

    /**
     * 获取状态显示名称
     *
     * @return 状态显示名称
     */
    public String getStatusDisplay() {
        if (enabled == null) {
            return "未知";
        }
        return enabled ? "启用" : "禁用";
    }

    /**
     * 获取状态颜色
     *
     * @return 状态颜色
     */
    public String getStatusColor() {
        if (enabled == null) {
            return "#999999";
        }
        return enabled ? "#52c41a" : "#ff4d4f";
    }

    /**
     * 是否有生效时间限制
     *
     * @return 是否有时间限制
     */
    public boolean hasTimeRestriction() {
        return effectiveTimeRanges != null && !effectiveTimeRanges.isEmpty();
    }

    /**
     * 是否有抑制时长限制
     *
     * @return 是否有时长限制
     */
    public boolean hasDurationLimit() {
        return durationMinutes != null && durationMinutes > 0;
    }

    /**
     * 获取格式化的抑制时长
     *
     * @return 格式化时长
     */
    public String getFormattedDuration() {
        if (durationMinutes == null || durationMinutes <= 0) {
            return "永久";
        }
        
        if (durationMinutes < 60) {
            return durationMinutes + "分钟";
        } else if (durationMinutes < 1440) {
            int hours = durationMinutes / 60;
            int minutes = durationMinutes % 60;
            return hours + "小时" + (minutes > 0 ? minutes + "分钟" : "");
        } else {
            int days = durationMinutes / 1440;
            int hours = (durationMinutes % 1440) / 60;
            return days + "天" + (hours > 0 ? hours + "小时" : "");
        }
    }

    /**
     * 获取条件数量
     *
     * @return 条件数量
     */
    public int getConditionCount() {
        return conditions != null ? conditions.size() : 0;
    }

    /**
     * 获取启用的条件数量
     *
     * @return 启用的条件数量
     */
    public int getEnabledConditionCount() {
        if (conditions == null) {
            return 0;
        }
        return (int) conditions.stream()
                .filter(condition -> condition.getEnabled() != null && condition.getEnabled())
                .count();
    }
}