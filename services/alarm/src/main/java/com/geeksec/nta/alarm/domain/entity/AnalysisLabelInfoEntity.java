package com.geeksec.nta.alarm.domain.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description：
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Table("tb_tag_info")
public class AnalysisLabelInfoEntity implements Serializable {


    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 标签ID //标签表
     */
    @Id(keyType = KeyType.Auto)
    @Column("tag_id")
    private Integer labelId;

    /**
     * 标签类型 //0删除，1生效
     */
    @Column("tag_type")
    private Integer labelType;

    /**
     * 标签备注
     */
    @Column("tag_remark")
    private String labelRemark;

    /**
     * 标签中文说明
     */
    @Column("tag_explain")
    private String labelExplain;

    /**
     * 标签内容
     */
    @Column("tag_text")
    private String labelText;

    /**
     * 标签数量
     */
    @Column("tag_num")
    private Integer labelNum;

    /**
     * //0代表ip目标，1代表端口目标，2代表应用目标，3代表域名目标，4代表证书目标，5代表MAC目标，6代表连接目标 {目标类型} , 7 指纹 9999 所有
     */
    @Column("tag_target_type")
    private Integer labelTargetType;

    /**
     * 默认黑名单
     */
    @Column("default_black_list")
    private Integer defaultBlackList;

    /**
     * 默认白名单
     */
    @Column("default_white_list")
    private Integer defaultWhiteList;

    /**
     * 黑名单
     */
    @Column("black_list")
    private Integer blackList;

    /**
     * 白名单
     */
    @Column("white_list")
    private Integer whiteList;

    /**
     * 创建时间
     */
    @Column("created_at")
    private Integer createdAt;

    /**
     * 最后创建时间
     */
    @Column("last_created_at")
    private Integer lastCreatedAt;

    /**
     * 0 其他 1 侦察探测 2 武器投递 3 攻击突防 4 命令控制 5。控守操作
     */
    @Column("tag_family")
    private Integer labelFamily;
}
