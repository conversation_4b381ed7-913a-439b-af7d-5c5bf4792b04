package com.geeksec.nta.alarm.domain.valueobject;

import java.util.Objects;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

/**
 * 订阅ID值对象
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Getter
@ToString
@EqualsAndHashCode
public final class SubscriptionId {

    private final Long value;

    private SubscriptionId(Long value) {
        this.value = Objects.requireNonNull(value, "订阅ID不能为空");
        if (value <= 0) {
            throw new IllegalArgumentException("订阅ID必须大于0");
        }
    }

    /**
     * 创建订阅ID
     *
     * @param value ID值
     * @return 订阅ID
     */
    public static SubscriptionId of(Long value) {
        return new SubscriptionId(value);
    }

    /**
     * 从字符串创建订阅ID
     *
     * @param value 字符串ID值
     * @return 订阅ID
     */
    public static SubscriptionId of(String value) {
        try {
            return new SubscriptionId(Long.parseLong(value));
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("无效的订阅ID格式: " + value, e);
        }
    }
}
