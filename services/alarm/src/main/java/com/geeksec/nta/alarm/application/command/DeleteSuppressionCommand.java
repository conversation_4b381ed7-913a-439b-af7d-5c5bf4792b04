package com.geeksec.nta.alarm.application.command;

import com.geeksec.nta.alarm.domain.valueobject.SuppressionId;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 删除抑制规则命令
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeleteSuppressionCommand {
    
    /**
     * 抑制规则ID
     */
    private SuppressionId suppressionId;
    
    /**
     * 操作人
     */
    private String operator;
    
    /**
     * 删除原因
     */
    private String reason;
    
    /**
     * 是否强制删除（忽略依赖检查）
     */
    @Builder.Default
    private Boolean forceDelete = false;
    
    /**
     * 验证命令参数
     */
    public void validate() {
        if (suppressionId == null) {
            throw new IllegalArgumentException("抑制规则ID不能为空");
        }
        if (operator == null || operator.trim().isEmpty()) {
            throw new IllegalArgumentException("操作人不能为空");
        }
        if (forceDelete == null) {
            throw new IllegalArgumentException("强制删除标志不能为空");
        }
    }
}
