package com.geeksec.nta.alarm.domain.aggregate.alarm;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.Data;

import java.util.Date;

@Table(value = "alarm_reason", schema = "nta" , comment = "告警-原因")
@Data
public class AlarmReason {

    @Id(keyType = KeyType.Generator)
    @Column(value = "id" , comment = "id")
    private String id;

    @Column(value = "alarm_id" , comment = "告警主表id")
    private String alarmId;

    @Column(value = "key" , comment = "key")
    private String key;

    @Column(value = "expected_value" , comment = "值")
    private String actualValue;

    @Column(value = "create_time" , comment = "创建时间")
    private Date createTime;

}