package com.geeksec.nta.alarm.interfaces.dto.subscription;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * 通知渠道 DTO
 * 用于配置告警通知的渠道信息
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NotificationChannelDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 渠道类型
     */
    private String channelType;

    /**
     * 是否启用
     */
    @Builder.Default
    private Boolean enabled = true;

    /**
     * 渠道配置参数
     */
    private Map<String, Object> config;

    /**
     * 通知模板ID
     */
    private String templateId;

    /**
     * 优先级（数值越大优先级越高）
     */
    @Builder.Default
    private Integer priority = 0;

    /**
     * 重试次数
     */
    @Builder.Default
    private Integer retryCount = 3;

    /**
     * 超时时间（秒）
     */
    @Builder.Default
    private Integer timeoutSeconds = 30;

    /**
     * 渠道描述
     */
    private String description;

    /**
     * 渠道类型枚举
     */
    public enum ChannelType {
        /** 邮件 */
        EMAIL,
        /** 短信 */
        SMS,
        /** 微信 */
        WECHAT,
        /** 钉钉 */
        DINGTALK,
        /** 企业微信 */
        WORK_WECHAT,
        /** Slack */
        SLACK,
        /** Webhook */
        WEBHOOK,
        /** 系统内通知 */
        SYSTEM,
        /** 移动推送 */
        PUSH
    }

    /**
     * 验证渠道配置是否有效
     *
     * @return 是否有效
     */
    public boolean isValid() {
        if (channelName == null || channelName.trim().isEmpty()) {
            return false;
        }
        if (channelType == null || channelType.trim().isEmpty()) {
            return false;
        }
        if (config == null || config.isEmpty()) {
            return false;
        }
        return validateChannelSpecificConfig();
    }

    /**
     * 验证特定渠道类型的配置
     *
     * @return 是否有效
     */
    private boolean validateChannelSpecificConfig() {
        switch (ChannelType.valueOf(channelType.toUpperCase())) {
            case EMAIL:
                return config.containsKey("recipients") && config.containsKey("subject");
            case SMS:
                return config.containsKey("phoneNumbers");
            case WECHAT:
            case DINGTALK:
            case WORK_WECHAT:
                return config.containsKey("webhookUrl") || config.containsKey("accessToken");
            case WEBHOOK:
                return config.containsKey("url") && config.containsKey("method");
            case SLACK:
                return config.containsKey("webhookUrl") || config.containsKey("channel");
            case SYSTEM:
                return config.containsKey("userIds") || config.containsKey("roleIds");
            case PUSH:
                return config.containsKey("deviceTokens") || config.containsKey("userIds");
            default:
                return true;
        }
    }

    /**
     * 获取配置值
     *
     * @param key 配置键
     * @return 配置值
     */
    public Object getConfigValue(String key) {
        return config != null ? config.get(key) : null;
    }

    /**
     * 获取字符串类型的配置值
     *
     * @param key 配置键
     * @return 字符串配置值
     */
    public String getConfigString(String key) {
        Object value = getConfigValue(key);
        return value != null ? value.toString() : null;
    }

    /**
     * 获取整数类型的配置值
     *
     * @param key 配置键
     * @return 整数配置值
     */
    public Integer getConfigInteger(String key) {
        Object value = getConfigValue(key);
        if (value instanceof Integer) {
            return (Integer) value;
        } else if (value instanceof String) {
            try {
                return Integer.parseInt((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }

    /**
     * 获取布尔类型的配置值
     *
     * @param key 配置键
     * @return 布尔配置值
     */
    public Boolean getConfigBoolean(String key) {
        Object value = getConfigValue(key);
        if (value instanceof Boolean) {
            return (Boolean) value;
        } else if (value instanceof String) {
            return Boolean.parseBoolean((String) value);
        }
        return null;
    }
}