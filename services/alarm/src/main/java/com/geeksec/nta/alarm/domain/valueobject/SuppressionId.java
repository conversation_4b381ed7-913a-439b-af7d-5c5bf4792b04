package com.geeksec.nta.alarm.domain.valueobject;

import java.util.Objects;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

/**
 * 抑制规则ID值对象
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Getter
@ToString
@EqualsAndHashCode
public final class SuppressionId {

    private final Integer value;

    private SuppressionId(Integer value) {
        this.value = Objects.requireNonNull(value, "抑制规则ID不能为空");
        if (value <= 0) {
            throw new IllegalArgumentException("抑制规则ID必须大于0");
        }
    }

    /**
     * 创建抑制规则ID
     *
     * @param value ID值
     * @return 抑制规则ID
     */
    public static SuppressionId of(Integer value) {
        return new SuppressionId(value);
    }

    /**
     * 从字符串创建抑制规则ID
     *
     * @param value 字符串ID值
     * @return 抑制规则ID
     */
    public static SuppressionId of(String value) {
        try {
            return new SuppressionId(Integer.parseInt(value));
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("无效的抑制规则ID格式: " + value, e);
        }
    }
}
