package com.geeksec.nta.alarm.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.geeksec.nta.alarm.dto.condition.AlarmCommonCondition;
import com.geeksec.nta.alarm.entity.AlarmBase;
import com.geeksec.nta.alarm.vo.AlarmTargetAggVo;
import com.geeksec.nta.alarm.vo.AlarmTypeAggVo;
import com.mybatisflex.core.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 告警Mapper接口
 *
 * 使用 MyBatis-Flex 1.10.9 BaseMapper
 *
 * <AUTHOR>
 * @since 3.0.0
 */


@Mapper
@DS("nta-postgresql")
public interface AlarmMapper extends BaseMapper<AlarmBase> {


    /**
     * 统计告警数量
     * @param condition
     * @return
     */
    AlarmTargetAggVo getAlarmTargetAgg(@Param("condition") AlarmCommonCondition condition);

    /**
     * 告警攻击链路
     * @param condition
     * @return
     */
    List<AlarmTypeAggVo.AttackChain> getModelAlarmAttackChainAggr(@Param("condition") AlarmCommonCondition condition);

    /**
     * 知识库分组
     * @param alarmIds
     * @return
     */
    List<AlarmTypeAggVo.AlarmKnowledge> getAlarmKnowledge(@Param("alarmIds") List<Long> alarmIds);

    void deleteAll();
}
