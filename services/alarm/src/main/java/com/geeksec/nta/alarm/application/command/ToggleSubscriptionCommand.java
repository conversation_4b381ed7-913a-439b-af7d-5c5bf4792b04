package com.geeksec.nta.alarm.application.command;

import com.geeksec.nta.alarm.domain.valueobject.SubscriptionId;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 切换订阅状态命令
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ToggleSubscriptionCommand {
    
    /**
     * 订阅ID
     */
    private SubscriptionId subscriptionId;
    
    /**
     * 是否启用
     */
    private Boolean enabled;
    
    /**
     * 操作人
     */
    private String operator;
    
    /**
     * 验证命令参数
     */
    public void validate() {
        if (subscriptionId == null) {
            throw new IllegalArgumentException("订阅ID不能为空");
        }
        if (enabled == null) {
            throw new IllegalArgumentException("启用状态不能为空");
        }
        if (operator == null || operator.trim().isEmpty()) {
            throw new IllegalArgumentException("操作人不能为空");
        }
    }
}