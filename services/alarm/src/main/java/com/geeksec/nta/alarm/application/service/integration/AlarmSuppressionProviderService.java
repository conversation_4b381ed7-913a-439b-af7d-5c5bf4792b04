package com.geeksec.nta.alarm.application.service.integration;

import java.util.List;

/**
 * 告警抑制规则提供服务
 * 为flink-jobs/alarm-processor提供抑制规则查询接口
 * 注意：实际的抑制检查逻辑在flink-jobs中执行，这里只提供规则数据
 *
 * <AUTHOR>
 * @since 3.0.0
 */
public interface AlarmSuppressionProviderService {

    /**
     * 获取所有活跃的抑制规则
     * 供flink-jobs调用获取最新的抑制规则
     *
     * @return 活跃抑制规则列表
     */
    List<SuppressionRuleDto> getActiveSuppressionRules();

    /**
     * 根据条件查询抑制规则
     *
     * @param victim 受害者IP（可选）
     * @param attacker 攻击者IP（可选）
     * @param label 告警标签（可选）
     * @return 匹配的抑制规则列表
     */
    List<SuppressionRuleDto> getSuppressionRulesByCondition(String victim, String attacker, String label);

    /**
     * 检查抑制规则是否存在（用于规则验证）
     *
     * @param victim 受害者IP
     * @param attacker 攻击者IP
     * @param label 告警标签
     * @return 是否存在匹配的规则
     */
    boolean hasMatchingRule(String victim, String attacker, String label);

    /**
     * 获取抑制规则的最后更新时间
     * 用于flink-jobs判断是否需要重新加载规则
     *
     * @return 最后更新时间戳
     */
    long getLastUpdateTime();

    /**
     * 获取抑制规则统计信息
     *
     * @return 统计信息
     */
    SuppressionStatistics getSuppressionStatistics();

    /**
     * 抑制规则DTO
     * 用于向flink-jobs提供规则数据
     */
    record SuppressionRuleDto(
        String ruleId,
        String victim,
        String attacker,
        String label,
        String description,
        boolean enabled,
        long createTime,
        long expireTime,
        String operator
    ) {}

    /**
     * 抑制统计信息
     */
    record SuppressionStatistics(
        long totalRules,
        long activeRules,
        long expiredRules,
        long disabledRules,
        long lastUpdateTime
    ) {}
}
