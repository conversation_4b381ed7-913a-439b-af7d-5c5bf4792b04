package com.geeksec.nta.alarm.integration;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.transaction.annotation.Transactional;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.nta.alarm.dto.subscription.CreateSubscriptionRequest;
import com.geeksec.nta.alarm.dto.subscription.NotificationChannelDto;
import com.geeksec.nta.alarm.dto.subscription.SubscriptionRuleDto;
import com.geeksec.nta.alarm.dto.subscription.TestSubscriptionRequest;
import com.geeksec.nta.alarm.dto.subscription.UpdateSubscriptionRequest;
import com.geeksec.nta.alarm.entity.AlarmSubscription;

/**
 * 告警订阅集成测试
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
class AlarmSubscriptionIntegrationTest {

        @Autowired
        private MockMvc mockMvc;

        @Autowired
        private ObjectMapper objectMapper;

        @Test
        void testCreateAndManageSubscription() throws Exception {
                // 1. 创建订阅
                CreateSubscriptionRequest createRequest = createTestSubscriptionRequest();

                MvcResult createResult = mockMvc.perform(post("/alarm/subscription")
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(createRequest)))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.success").value(true))
                                .andExpect(jsonPath("$.data").isNotEmpty())
                                .andReturn();

                String subscriptionId = extractSubscriptionId(createResult);

                // 2. 获取订阅详情
                mockMvc.perform(get("/alarm/subscription/{id}", subscriptionId))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.success").value(true))
                                .andExpect(jsonPath("$.data.id").value(subscriptionId))
                                .andExpect(jsonPath("$.data.subscriptionName").value("集成测试订阅"))
                                .andExpect(jsonPath("$.data.enabled").value(true));

                // 3. 更新订阅
                UpdateSubscriptionRequest updateRequest = createUpdateSubscriptionRequest();

                mockMvc.perform(put("/alarm/subscription/{id}", subscriptionId)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateRequest)))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.success").value(true));

                // 4. 验证更新结果
                mockMvc.perform(get("/alarm/subscription/{id}", subscriptionId))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.data.subscriptionName").value("更新后的订阅"));

                // 5. 禁用订阅
                mockMvc.perform(put("/alarm/subscription/{id}/status", subscriptionId)
                                .param("enabled", "false"))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.success").value(true));

                // 6. 验证禁用结果
                mockMvc.perform(get("/alarm/subscription/{id}", subscriptionId))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.data.enabled").value(false));

                // 7. 删除订阅
                mockMvc.perform(delete("/alarm/subscription/{id}", subscriptionId))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.success").value(true));

                // 8. 验证删除结果
                mockMvc.perform(get("/alarm/subscription/{id}", subscriptionId))
                                .andExpect(status().isNotFound());
        }

        @Test
        void testSubscriptionList() throws Exception {
                // 1. 创建多个订阅
                for (int i = 1; i <= 3; i++) {
                        CreateSubscriptionRequest request = createTestSubscriptionRequest();
                        request.setSubscriptionName("测试订阅" + i);

                        mockMvc.perform(post("/alarm/subscription")
                                        .contentType(MediaType.APPLICATION_JSON)
                                        .content(objectMapper.writeValueAsString(request)))
                                        .andExpect(status().isOk());
                }

                // 2. 获取订阅列表
                mockMvc.perform(get("/alarm/subscription")
                                .param("page", "1")
                                .param("size", "10"))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.success").value(true))
                                .andExpect(jsonPath("$.data.records").isArray())
                                .andExpect(jsonPath("$.data.total").value(3));

                // 3. 搜索订阅
                mockMvc.perform(get("/alarm/subscription")
                                .param("page", "1")
                                .param("size", "10")
                                .param("keyword", "测试订阅1"))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.success").value(true))
                                .andExpect(jsonPath("$.data.total").value(1))
                                .andExpect(jsonPath("$.data.records[0].subscriptionName").value("测试订阅1"));
        }

        @Test
        void testSubscriptionRuleTesting() throws Exception {
                // 1. 创建测试请求
                TestSubscriptionRequest testRequest = new TestSubscriptionRequest();

                List<SubscriptionRuleDto> rules = Arrays.asList(
                                SubscriptionRuleDto.createEqualsRule("alarmType", "恶意软件"),
                                SubscriptionRuleDto.createContainsRule("srcIp", "192.168.1"),
                                SubscriptionRuleDto.createRegexRule("dstIp", "10\\.0\\.0\\.\\d+"));
                testRequest.setMatchRules(rules);

                Map<String, Object> testData = new HashMap<>();
                testData.put("alarmType", "恶意软件");
                testData.put("srcIp", "*************");
                testData.put("dstIp", "*********");
                testRequest.setTestData(testData);

                // 2. 执行测试
                mockMvc.perform(post("/alarm/subscription/test")
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(testRequest)))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.success").value(true))
                                .andExpect(jsonPath("$.data.matched").value(true))
                                .andExpect(jsonPath("$.data.matchedRulesCount").value(3))
                                .andExpect(jsonPath("$.data.totalRulesCount").value(3))
                                .andExpect(jsonPath("$.data.ruleDetails").isArray())
                                .andExpect(jsonPath("$.data.ruleDetails[0].matched").value(true))
                                .andExpect(jsonPath("$.data.ruleDetails[1].matched").value(true))
                                .andExpect(jsonPath("$.data.ruleDetails[2].matched").value(true));
        }

        @Test
        void testSyncEndpoints() throws Exception {
                // 1. 创建一个启用的订阅
                CreateSubscriptionRequest createRequest = createTestSubscriptionRequest();

                MvcResult createResult = mockMvc.perform(post("/alarm/subscription")
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(createRequest)))
                                .andExpect(status().isOk())
                                .andReturn();

                // 2. 测试获取启用的订阅配置
                mockMvc.perform(get("/alarm/subscription/sync/active"))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.success").value(true))
                                .andExpect(jsonPath("$.data").isArray())
                                .andExpect(jsonPath("$.data[0].subscriptionId").isNotEmpty())
                                .andExpect(jsonPath("$.data[0].enabled").value(true));

                // 3. 测试健康检查
                mockMvc.perform(get("/alarm/subscription/sync/health"))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.success").value(true))
                                .andExpect(jsonPath("$.data").value("OK"));

        }

        /**
         * 创建测试订阅请求
         */
        private CreateSubscriptionRequest createTestSubscriptionRequest() {
                CreateSubscriptionRequest request = new CreateSubscriptionRequest();
                request.setSubscriptionName("集成测试订阅");
                request.setDescription("这是一个集成测试订阅");
                request.setPriorityLevel(1);
                request.setFrequencyType(AlarmSubscription.FrequencyType.REAL_TIME);

                // 匹配规则
                List<SubscriptionRuleDto> rules = Arrays.asList(
                                SubscriptionRuleDto.createEqualsRule("alarmType", "恶意软件"),
                                SubscriptionRuleDto.createContainsRule("srcIp", "192.168.1"));
                request.setMatchRules(rules);

                // 通知渠道
                List<NotificationChannelDto> channels = Arrays.asList(
                                NotificationChannelDto.createEmailChannel("<EMAIL>", "default_email_template"),
                                NotificationChannelDto.createKafkaChannel("alarm-notifications",
                                                "default_kafka_template"));
                request.setNotificationChannels(channels);

                return request;
        }

        /**
         * 创建更新订阅请求
         */
        private UpdateSubscriptionRequest createUpdateSubscriptionRequest() {
                UpdateSubscriptionRequest request = new UpdateSubscriptionRequest();
                request.setSubscriptionName("更新后的订阅");
                request.setDescription("这是更新后的描述");
                request.setPriorityLevel(2);
                request.setFrequencyType(AlarmSubscription.FrequencyType.INTERVAL);

                // 更新匹配规则
                List<SubscriptionRuleDto> rules = Arrays.asList(
                                SubscriptionRuleDto.createEqualsRule("alarmType", "网络攻击"),
                                SubscriptionRuleDto.createContainsRule("dstIp", "10.0.0"));
                request.setMatchRules(rules);

                // 更新通知渠道
                List<NotificationChannelDto> channels = Arrays.asList(
                                NotificationChannelDto.createEmailChannel("<EMAIL>",
                                                "default_email_template"));
                request.setNotificationChannels(channels);

                return request;
        }

        /**
         * 从创建结果中提取订阅ID
         */
        private String extractSubscriptionId(MvcResult result) throws Exception {
                String responseContent = result.getResponse().getContentAsString();
                Map<String, Object> response = objectMapper.readValue(responseContent, Map.class);
                return (String) response.get("data");
        }
}
