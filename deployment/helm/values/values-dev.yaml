# values-dev.yaml - 开发环境配置
# 开发环境为在线环境，针对开发测试优化（资源较少）

infrastructure:
  postgresql:
    enabled: true
    instances: 1
    
    primary:
      persistence:
        size: "20Gi"
        storageClass: "standard"
      resources:
        requests:
          memory: "2Gi"
          cpu: "1000m"
        limits:
          memory: "4Gi"
          cpu: "2000m"
    
    postgresql:
      parameters:
        shared_buffers: "512MB"
        work_mem: "128MB"
        maintenance_work_mem: "256MB"
        max_connections: "100"
    
    # 开发环境数据导入配置
    dataImport:
      enabled: true
      csvHandling:
        singleConfigMap: false  # 开发环境使用分片方案
        multiConfigMap: true
        tempStorageSize: "5Gi"

    # 大文件存储配置
    largeData:
      storage:
        size: "20Gi"  # 开发环境较小的存储
        storageClass: "standard"

# 开发环境镜像配置（在线环境）
images:
  postgresql:
    repository: "postgres"
    tag: "15-alpine"
    pullPolicy: "Always"  # 开发环境总是拉取最新镜像（在线）
  
  alpine:
    repository: "alpine"
    tag: "3.18"
    pullPolicy: "Always"  # 开发环境在线拉取

# 开发环境存储配置（在线环境）
storageClass:
  name: "standard"
  provisioner: "kubernetes.io/gce-pd"
  volumeBindingMode: "Immediate"

# 开发环境网络配置
networkPolicy:
  enabled: false  # 开发环境通常不启用网络策略

# 开发环境监控配置
monitoring:
  enabled: false  # 开发环境可选择关闭监控以节省资源

# 开发环境特定配置
development:
  # 在线环境配置
  online: true
  # 调试模式
  debug: true
  # 热重载
  hotReload: true
