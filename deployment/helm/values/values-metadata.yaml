# values-metadata.yaml - 元数据资源配置文件
# 包含元数据资源的版本管理和自动更新配置

# 元数据配置
metadata:
  # 元数据版本管理
  version: "1.0.0"

  # 恶意域名数据版本
  maliciousDomains:
    version: "1.0.0"

  # GeoIP数据版本
  geoip:
    version: "1.0.0"

  # 自动更新配置
  autoUpdate:
    enabled: true
    # 更新调度：每天凌晨2点
    schedule: "0 2 * * *"
    # 更新源列表
    sources:
      - "tranco"
      - "geoip"
      - "public-suffix"
      - "malicious-domains"

    # 更新策略
    strategy:
      # 滚动更新：逐个更新Flink作业
      type: "rolling"
      # 更新超时时间
      timeout: "30m"
      # 失败重试次数
      retries: 3

  # 外部数据源配置
  dataSources:
    # Tranco域名排名
    tranco:
      enabled: true
      # Tranco列表ID（可在https://tranco-list.eu/获取）
      listId: "NNJPW"
      # 更新URL模板
      urlTemplate: "https://tranco-list.eu/download/NNJPW/1000000"

    # GeoIP数据库
    geoip:
      enabled: true
      # 数据库下载URL
      databases:
        - name: "GeoLite2-City"
          url: "https://git.io/GeoLite2-City.mmdb"
          filename: "GeoLite2-City.mmdb"
        - name: "GeoLite2-ASN"
          url: "https://git.io/GeoLite2-ASN.mmdb"
          filename: "GeoLite2-ASN.mmdb"

    # Public Suffix List
    publicSuffix:
      enabled: true
      # 更新URL
      url: "https://publicsuffix.org/list/public_suffix_list.dat"

    # 恶意域名列表
    maliciousDomains:
      enabled: true
      # 数据源列表
      sources:
        - name: "malicious-domains-aa"
          url: "https://raw.githubusercontent.com/romainmarcoux/malicious-domains/main/full-domains-aa.txt"
          format: "text"
          filename: "full-domains-aa.txt"
        - name: "malicious-domains-ab"
          url: "https://raw.githubusercontent.com/romainmarcoux/malicious-domains/main/full-domains-ab.txt"
          format: "text"
          filename: "full-domains-ab.txt"
        - name: "malicious-domains-ac"
          url: "https://raw.githubusercontent.com/romainmarcoux/malicious-domains/main/full-domains-ac.txt"
          format: "text"
          filename: "full-domains-ac.txt"

  # 存储配置
  storage:
    # 使用MinIO存储更新的资源文件
    type: "minio"
    # 存储桶
    bucket: "metadata-resources"
    # 路径前缀
    pathPrefix: "resources/"
    # 备份保留策略
    backup:
      enabled: true
      # 保留最近5个版本
      retentionCount: 5
      # 保留30天
      retentionDays: 30

  # 监控和告警配置
  monitoring:
    enabled: true
    # 更新失败告警
    alerts:
      updateFailure:
        enabled: true
        # 告警阈值：连续失败3次
        threshold: 3
      # 数据过期告警
      dataExpiry:
        enabled: true
        # 告警阈值：数据超过7天未更新
        threshold: "7d"

  # 资源文件大小限制
  limits:
    # 单个ConfigMap最大大小（Kubernetes限制为1MB）
    configMapSize: "1MB"
    # 大文件处理策略：split（分割）或 external（外部存储）
    largeFileStrategy: "external"
    # 分割阈值
    splitThreshold: "500KB"
