{{/*
Kafka configuration helper template
*/}}
{{- define "nta.kafkaConfig" -}}
host: {{ .Values.infrastructure.kafka.host }}
port: {{ .Values.infrastructure.kafka.port }}
security:
  protocol: {{ .Values.infrastructure.kafka.security.protocol }}
  mechanism: {{ .Values.infrastructure.kafka.security.mechanism }}
topics:
  cert: {{ .Values.infrastructure.kafka.topics.cert }}
  systemBuiltInCertificates: {{ .Values.infrastructure.kafka.topics.systemBuiltInCertificates }}
  meta: {{ .Values.infrastructure.kafka.topics.meta }}
  patternToggle: {{ .Values.infrastructure.kafka.topics.patternToggle }}
  modelConfig: {{ .Values.infrastructure.kafka.topics.modelConfig }}
{{- end -}}
