{{- if .Values.services.knowledge-base.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: knowledge-base-service
  namespace: {{ .Values.global.namespace | default "nta" }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
spec:
  type: ClusterIP
  ports:
    - name: http
      port: {{ .Values.services.knowledge-base.service.port }}
      targetPort: {{ .Values.services.knowledge-base.service.targetPort }}
      protocol: TCP
  selector:
    app: knowledge-base
{{- end }}
