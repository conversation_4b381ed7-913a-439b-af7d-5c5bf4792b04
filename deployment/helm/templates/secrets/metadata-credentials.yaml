# metadata-credentials.yaml
# 元数据更新所需的凭据配置

{{- if and .Values.infrastructure.flink.enabled .Values.metadata.autoUpdate.enabled }}

# GeoIP凭据配置（可选）
# 如果需要使用MaxMind官方API，可以取消注释以下配置
{{- if and .Values.metadata.dataSources.geoip.enabled .Values.metadata.dataSources.geoip.licenseKey }}
---
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "nta.fullname" . }}-geoip-credentials
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
    app.kubernetes.io/component: metadata-credentials
type: Opaque
data:
  # MaxMind GeoIP许可证密钥
  license-key: {{ .Values.metadata.dataSources.geoip.licenseKey | b64enc | quote }}
{{- end }}

{{- if .Values.metadata.dataSources.tranco.enabled }}
---
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "nta.fullname" . }}-tranco-credentials
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
    app.kubernetes.io/component: metadata-credentials
type: Opaque
data:
  # Tranco API密钥（如果需要）
  api-key: {{ .Values.metadata.dataSources.tranco.apiKey | default "" | b64enc | quote }}
{{- end }}

{{- if .Values.metadata.dataSources.maliciousDomains.enabled }}
---
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "nta.fullname" . }}-threat-intel-credentials
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
    app.kubernetes.io/component: metadata-credentials
type: Opaque
data:
  # 威胁情报源API密钥
  {{- range .Values.metadata.dataSources.maliciousDomains.sources }}
  {{- if .apiKey }}
  {{ .name }}-api-key: {{ .apiKey | b64enc | quote }}
  {{- end }}
  {{- end }}
{{- end }}

{{- end }}
