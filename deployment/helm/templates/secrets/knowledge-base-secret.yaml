{{- if .Values.services.knowledge-base.enabled -}}
apiVersion: v1
kind: Secret
metadata:
  name: knowledge-base-secret
  namespace: {{ .Values.global.namespace | default "nta" }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
type: Opaque
data:
  db-username: {{ .Values.global.database.username | default "bnRhX3VzZXI=" | b64enc | quote }}  # Default: nta_user
  db-password: {{ .Values.global.database.password | default "bnRhX3Bhc3N3b3Jk" | b64enc | quote }}  # Default: nta_password
  redis-password: {{ .Values.global.redis.password | default "" | b64enc | quote }}
{{- end }}
