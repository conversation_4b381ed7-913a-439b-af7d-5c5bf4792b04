{{- if .Values.infrastructure.postgresql.enabled }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "nta.credentialsSecretName" (dict "type" "postgresql" "root" $) }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
type: Opaque
data:
  username: {{ .Values.infrastructure.postgresql.credentials.username | default "postgres" | b64enc }}
  password: {{ .Values.infrastructure.postgresql.credentials.postgresPassword.value | b64enc }}
  postgres-password: {{ .Values.infrastructure.postgresql.credentials.postgresPassword.value | b64enc }}
  
---
# 应用程序使用的PostgreSQL凭证
apiVersion: v1
kind: Secret
metadata:
  name: postgresql-credentials
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
type: Opaque
data:
  username: {{ "nta_user" | b64enc }}
  password: {{ .Values.infrastructure.postgresql.credentials.password.value | b64enc }}
  host: {{ printf "%s-postgresql-rw" .Release.Name | b64enc }}
  port: {{ "5432" | b64enc }}
{{- end }}
