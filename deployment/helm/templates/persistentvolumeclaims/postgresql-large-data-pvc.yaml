{{- if .Values.infrastructure.postgresql.enabled }}
# PostgreSQL 大文件数据 PVC
# 用于存储大型 CSV 文件，避免 ConfigMap 大小限制
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ .Release.Name }}-postgresql-large-data
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
    app.kubernetes.io/component: postgresql-data
spec:
  accessModes:
    - ReadOnlyMany  # 多个 Pod 可以同时读取
  resources:
    requests:
      storage: {{ .Values.infrastructure.postgresql.largeData.storage.size | default "50Gi" }}
  {{- if .Values.infrastructure.postgresql.largeData.storage.storageClass }}
  storageClassName: {{ .Values.infrastructure.postgresql.largeData.storage.storageClass }}
  {{- end }}
{{- end }}
