# ClusterRole for service discovery
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: nta-service-discovery
  labels:
    {{- include "nta.labels" . | nindent 4 }}
    app.kubernetes.io/component: rbac
rules:
# 服务发现权限
- apiGroups: [""]
  resources: ["services", "endpoints"]
  verbs: ["get", "list", "watch"]
# Pod信息读取权限（用于负载均衡）
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]
# 命名空间读取权限
- apiGroups: [""]
  resources: ["namespaces"]
  verbs: ["get", "list", "watch"]
---
# ClusterRole for configuration management
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: nta-config-management
  labels:
    {{- include "nta.labels" . | nindent 4 }}
    app.kubernetes.io/component: rbac
rules:
# ConfigMap读取权限
- apiGroups: [""]
  resources: ["configmaps"]
  verbs: ["get", "list", "watch"]
# Secret读取权限
- apiGroups: [""]
  resources: ["secrets"]
  verbs: ["get", "list", "watch"]
# 事件读取权限（用于配置重新加载）
- apiGroups: [""]
  resources: ["events"]
  verbs: ["get", "list", "watch"]
---
# ClusterRole for microservices (combines discovery and config)
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: nta-microservice
  labels:
    {{- include "nta.labels" . | nindent 4 }}
    app.kubernetes.io/component: rbac
rules:
# 服务发现权限
- apiGroups: [""]
  resources: ["services", "endpoints", "pods"]
  verbs: ["get", "list", "watch"]
# 配置管理权限
- apiGroups: [""]
  resources: ["configmaps", "secrets"]
  verbs: ["get", "list", "watch"]
# 命名空间和事件权限
- apiGroups: [""]
  resources: ["namespaces", "events"]
  verbs: ["get", "list", "watch"]
# 节点信息读取权限（可选，用于拓扑感知）
- apiGroups: [""]
  resources: ["nodes"]
  verbs: ["get", "list", "watch"]
