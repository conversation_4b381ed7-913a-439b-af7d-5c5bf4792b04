package com.geeksec.alarm.notification.source;

import org.apache.flink.api.connector.source.Boundedness;
import org.apache.flink.api.connector.source.Source;
import org.apache.flink.api.connector.source.SourceReader;
import org.apache.flink.api.connector.source.SourceReaderContext;
import org.apache.flink.api.connector.source.SplitEnumerator;
import org.apache.flink.api.connector.source.SplitEnumeratorContext;
import org.apache.flink.core.io.SimpleVersionedSerializer;

import com.geeksec.alarm.notification.client.AlarmServiceClient;
import com.geeksec.alarm.notification.model.NotificationSubscription;

import lombok.extern.slf4j.Slf4j;

/**
 * 订阅配置数据源
 * 负责从告警服务获取订阅配置数据
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
public class SubscriptionConfigSource
        implements Source<NotificationSubscription, SubscriptionConfigSplit, SubscriptionConfigEnumeratorState> {

    private static final long serialVersionUID = 1L;

    private final AlarmServiceClient alarmServiceClient;

    public SubscriptionConfigSource(AlarmServiceClient alarmServiceClient) {
        this.alarmServiceClient = alarmServiceClient;
    }

    @Override
    public Boundedness getBoundedness() {
        // 这是一个有界数据源，读取完初始配置后就结束
        return Boundedness.BOUNDED;
    }

    @Override
    public SourceReader<NotificationSubscription, SubscriptionConfigSplit> createReader(
            SourceReaderContext readerContext) {
        log.info("创建订阅配置数据源读取器");
        return new SubscriptionConfigSourceReader(alarmServiceClient, readerContext);
    }

    @Override
    public SplitEnumerator<SubscriptionConfigSplit, SubscriptionConfigEnumeratorState> createEnumerator(
            SplitEnumeratorContext<SubscriptionConfigSplit> enumContext) {
        log.info("创建订阅配置数据源分片枚举器");
        return new SubscriptionConfigSplitEnumerator(enumContext);
    }

    @Override
    public SplitEnumerator<SubscriptionConfigSplit, SubscriptionConfigEnumeratorState> restoreEnumerator(
            SplitEnumeratorContext<SubscriptionConfigSplit> enumContext,
            SubscriptionConfigEnumeratorState checkpoint) {
        log.info("恢复订阅配置数据源分片枚举器");
        return new SubscriptionConfigSplitEnumerator(enumContext, checkpoint);
    }

    @Override
    public SimpleVersionedSerializer<SubscriptionConfigSplit> getSplitSerializer() {
        return new SubscriptionConfigSplitSerializer();
    }

    @Override
    public SimpleVersionedSerializer<SubscriptionConfigEnumeratorState> getEnumeratorCheckpointSerializer() {
        return new SubscriptionConfigEnumeratorStateSerializer();
    }

    /**
     * 创建默认的订阅配置数据源
     *
     * @param alarmServiceClient 告警服务客户端
     * @return 订阅配置数据源
     */
    public static SubscriptionConfigSource createDefault(AlarmServiceClient alarmServiceClient) {
        return new SubscriptionConfigSource(alarmServiceClient);
    }
}
