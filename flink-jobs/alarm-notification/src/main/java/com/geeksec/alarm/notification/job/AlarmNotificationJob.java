package com.geeksec.alarm.notification.job;

import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.runtime.state.hashmap.HashMapStateBackend;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.sink.v2.DiscardingSink;

import com.geeksec.alarm.notification.config.AlarmNotificationConfig;
import com.geeksec.alarm.notification.pipeline.NotificationPipeline;
import com.geeksec.alarm.notification.sender.NotificationSender;

import lombok.extern.slf4j.Slf4j;

/**
 * 告警通知作业主类
 * 
 * 业务流程：
 * 1. 从 Kafka 接收来自 alarm-processor 的处理后告警数据
 * 2. 从告警服务获取订阅配置，并监听配置变更
 * 3. 根据订阅规则匹配告警并发送通知
 * 4. 支持邮件和 Kafka 两种通知方式
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
public class AlarmNotificationJob {

    public static void main(String[] args) throws Exception {
        log.info("启动告警通知作业");

        try {
            // 1. 获取配置（命令行参数）
            final ParameterTool parameterTool = ParameterTool.fromArgs(args);

            // 2. 创建配置对象
            final AlarmNotificationConfig config = AlarmNotificationConfig.fromParameterTool(parameterTool);

            // 打印配置信息
            config.printConfig();

            // 3. 创建执行环境
            final StreamExecutionEnvironment env = createExecutionEnvironment(config);

            // 4. 设置全局参数
            env.getConfig().setGlobalJobParameters(parameterTool);

            // 5. 配置检查点
            configureCheckpointing(env, config);

            // 6. 根据配置选择处理流水线
            // 创建通知发送器和流水线
            NotificationSender notificationSender = new NotificationSender(config);
            NotificationPipeline pipeline = NotificationPipeline.createDefault(config, notificationSender);
            DataStream<Void> notificationStream = pipeline.buildPipeline(env);

            // 添加丢弃sink
            notificationStream.sinkTo(new DiscardingSink<>())
                    .name("discard-sink")
                    .setParallelism(1);

            log.info("使用直接通知模式，不记录通知结果");

            // 8. 执行作业
            log.info("开始执行告警通知作业...");
            env.execute(config.getJobName());

            log.info("告警通知作业执行完成");

        } catch (Exception e) {
            log.error("告警通知作业执行失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 创建执行环境
     */
    private static StreamExecutionEnvironment createExecutionEnvironment(AlarmNotificationConfig config) {
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();

        // 设置并行度
        env.setParallelism(config.getJobParallelism());

        // 设置状态后端（保持使用旧方式以确保兼容性）
        env.setStateBackend(new HashMapStateBackend());

        log.info("执行环境创建完成，并行度: {}", config.getJobParallelism());
        return env;
    }

    /**
     * 配置检查点
     */
    private static void configureCheckpointing(StreamExecutionEnvironment env, AlarmNotificationConfig config) {
        // 启用检查点（保持使用旧方式以确保兼容性）
        env.enableCheckpointing(config.getCheckpointInterval());

        // 设置检查点模式
        env.getCheckpointConfig().setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);

        // 设置检查点超时时间
        env.getCheckpointConfig().setCheckpointTimeout(config.getCheckpointTimeout());

        // 设置最小暂停间隔
        env.getCheckpointConfig().setMinPauseBetweenCheckpoints(config.getMinPauseBetweenCheckpoints());

        // 设置最大并发检查点数
        env.getCheckpointConfig().setMaxConcurrentCheckpoints(config.getMaxConcurrentCheckpoints());

        // 设置检查点清理策略
        env.getCheckpointConfig().setExternalizedCheckpointCleanup(
                CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);

        log.info("检查点配置完成，间隔: {}ms, 超时: {}ms",
                config.getCheckpointInterval(), config.getCheckpointTimeout());
    }
}
