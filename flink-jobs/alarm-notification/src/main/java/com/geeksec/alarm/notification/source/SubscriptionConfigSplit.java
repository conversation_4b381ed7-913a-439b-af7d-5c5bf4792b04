package com.geeksec.alarm.notification.source;

import org.apache.flink.api.connector.source.SourceSplit;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 订阅配置数据源分片
 * 用于新的 Source 接口实现
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SubscriptionConfigSplit implements SourceSplit {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 分片ID
     */
    private String splitId;
    
    /**
     * 是否已完成
     */
    private boolean finished;
    
    /**
     * 创建默认分片
     */
    public static SubscriptionConfigSplit createDefault() {
        return new SubscriptionConfigSplit("subscription-config-split-0", false);
    }
    
    /**
     * 标记分片为已完成
     */
    public void markFinished() {
        this.finished = true;
    }
    
    @Override
    public String splitId() {
        return splitId;
    }
}
