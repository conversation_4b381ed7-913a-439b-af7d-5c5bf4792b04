package com.geeksec.alarm.notification.source;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

import org.apache.flink.api.connector.source.SplitEnumerator;
import org.apache.flink.api.connector.source.SplitEnumeratorContext;
import org.apache.flink.api.connector.source.SplitsAssignment;

import lombok.extern.slf4j.Slf4j;

/**
 * 订阅配置数据源分片枚举器
 * 负责管理和分配分片
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
public class SubscriptionConfigSplitEnumerator implements SplitEnumerator<SubscriptionConfigSplit, SubscriptionConfigEnumeratorState> {
    
    private final SplitEnumeratorContext<SubscriptionConfigSplit> context;
    private SubscriptionConfigEnumeratorState state;
    
    public SubscriptionConfigSplitEnumerator(SplitEnumeratorContext<SubscriptionConfigSplit> context) {
        this.context = context;
        this.state = SubscriptionConfigEnumeratorState.createDefault();
    }
    
    public SubscriptionConfigSplitEnumerator(SplitEnumeratorContext<SubscriptionConfigSplit> context, 
                                           SubscriptionConfigEnumeratorState state) {
        this.context = context;
        this.state = state != null ? state : SubscriptionConfigEnumeratorState.createDefault();
    }
    
    @Override
    public void start() {
        log.info("启动订阅配置分片枚举器");
        // 立即分配分片给所有可用的读取器
        assignSplitsToAvailableReaders();
    }
    
    @Override
    public void handleSplitRequest(int subtaskId, String requesterHostname) {
        log.info("处理分片请求: subtaskId={}, hostname={}", subtaskId, requesterHostname);
        
        // 为请求的子任务分配分片
        if (!state.isInitialized()) {
            SubscriptionConfigSplit split = SubscriptionConfigSplit.createDefault();
            assignSplitToReader(subtaskId, split);
            state.addAssignedSplitId(split.getSplitId());
            state.markInitialized();
        } else {
            // 如果已经初始化，通知没有更多分片
            context.signalNoMoreSplits(subtaskId);
        }
    }
    
    @Override
    public void addSplitsBack(List<SubscriptionConfigSplit> splits, int subtaskId) {
        log.info("添加分片回退: splits={}, subtaskId={}", splits.size(), subtaskId);
        // 对于这个简单的数据源，我们不需要处理分片回退
        // 因为数据是一次性读取的
    }
    
    @Override
    public void addReader(int subtaskId) {
        log.info("添加读取器: subtaskId={}", subtaskId);
        
        // 为新的读取器分配分片
        if (!state.isInitialized()) {
            SubscriptionConfigSplit split = SubscriptionConfigSplit.createDefault();
            assignSplitToReader(subtaskId, split);
            state.addAssignedSplitId(split.getSplitId());
            state.markInitialized();
        } else {
            // 如果已经初始化，通知没有更多分片
            context.signalNoMoreSplits(subtaskId);
        }
    }
    
    @Override
    public SubscriptionConfigEnumeratorState snapshotState(long checkpointId) throws Exception {
        log.debug("创建枚举器检查点快照: checkpointId={}", checkpointId);
        return state;
    }
    
    @Override
    public void close() throws IOException {
        log.info("关闭订阅配置分片枚举器");
    }
    
    /**
     * 为所有可用的读取器分配分片
     */
    private void assignSplitsToAvailableReaders() {
        if (!state.isInitialized() && context.registeredReaders().size() > 0) {
            // 只为第一个读取器分配分片，因为我们只需要一个分片
            int firstReaderId = context.registeredReaders().keySet().iterator().next();
            SubscriptionConfigSplit split = SubscriptionConfigSplit.createDefault();
            assignSplitToReader(firstReaderId, split);
            state.addAssignedSplitId(split.getSplitId());
            state.markInitialized();
            
            // 通知其他读取器没有更多分片
            for (int readerId : context.registeredReaders().keySet()) {
                if (readerId != firstReaderId) {
                    context.signalNoMoreSplits(readerId);
                }
            }
        }
    }
    
    /**
     * 为指定读取器分配分片
     */
    private void assignSplitToReader(int subtaskId, SubscriptionConfigSplit split) {
        Map<Integer, List<SubscriptionConfigSplit>> assignment = new HashMap<>();
        assignment.put(subtaskId, List.of(split));
        
        context.assignSplits(new SplitsAssignment<>(assignment));
        log.info("分配分片给读取器: subtaskId={}, splitId={}", subtaskId, split.getSplitId());
    }
}
