<template>
  <div class="log-table">
    <el-table :data="tableData" style="width: 100%" :show-header="false">
      <el-table-column prop="name" width="120" show-overflow-tooltip>
        <template #default="{ row }">
          {{ row.name || "" }}
        </template>
      </el-table-column>
      <el-table-column prop="describe" show-overflow-tooltip width="150">
        <template #default="{ row }">
          <span class="info-color"> {{ row.describe || "" }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="value" show-overflow-tooltip>
        <template #default="{ row }">
          <span v-if="Array.isArray(row.value)">
            {{ row.value.length ? row.value.join(",") : "" }}
          </span>
          <span
            v-else-if="
              row.describe === 'Extension' ||
                row.describe === 'Issuer' ||
                row.describe === 'Subject'
            "
          ></span>
          <span v-else>
            {{
              row.value
                ? ["NotAfter", "NotBefore"].includes(
                  row.describe
                )
                  ? $formatDateOne(row.value)
                  : row.value
                : "-"
            }}
          </span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
import { getTemplate } from "@/api/sessionList/sessionlist";
export default {
  name: "LogTable",
  props: {
    jsonData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      tableData: [],
      list: [],
      TAG_TABLE: {},
    };
  },
  watch: {
    jsonData: {
      handler(val) {
        if (Object.keys(val).length) {
          this.getLogTemplate();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    init() {
      this.tableData = [];
      let { TAG_TABLE } = this;
      for (let key in TAG_TABLE) {
        if (key.startsWith("Extension.")) {
          this.formatData(key, "Extension");
        } else if (key.startsWith("Issuer.")) {
          this.formatData(key, "Issuer");
        } else if (key.startsWith("Subject.")) {
          this.formatData(key, "Subject");
        } else {
          let obj = {
            name: TAG_TABLE[key],
            describe: key,
            value: this.jsonData[key],
          };
          this.tableData.push(obj);
        }
      }
    },
    formatData(startKey, name) {
      let { TAG_TABLE } = this;
      let childKey = startKey.split(".")[1];
      let obj = {
        name: TAG_TABLE[startKey],
        describe: startKey.split(".")[1],
        value: this.jsonData[name]?.[childKey],
        isFather:true,
        allKey:startKey
      };
      this.tableData.push(obj);
    },
    // 获取模板数据
    async getLogTemplate() {
      const res = await getTemplate({ user_id: 1 });
      this.TAG_TABLE = res.data;
      this.init();
    },
  },
};
</script>
  
  <style lang="scss" scoped>
  .log-table {
    margin-top: 24px;
    .row {
      display: flex;
      align-items: center;
      color: #2c2c35;
      min-height: 22px;
      border-bottom: 1px solid #f2f3f7;
      padding: 8px 0;
      &:last-child {
        border-bottom: none;
      }
      .name {
        width: 112px;
        display: flex;
        .label {
          max-width: 80px;
        //   @include text-overflow;
        }
        .icon-a-12_remark {
          margin-left: 4px;
          color: #116ef9;
          cursor: pointer;
          opacity: 0;
          width: 30px;
        }
        // &:hover .icon-a-12_remark {
        //   opacity: 1;
        // }
      }
      .describe {
        width: 116px;
        color: #9999a1;
      }
      .value {
        flex: 1;
      }
    }
    .title {
      display: flex;
      align-items: center;
      white-space: nowrap;
      .edit {
        margin: 0 4px;
        width: 20px;
        .icon-a-12_remark {
          font-size: 12px;
          display: none;
        }
      }
      .name {
        max-width: 90px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      &:hover {
        .icon-a-12_remark {
          display: block;
          color: #116ef9;
          cursor: pointer;
        }
      }
    }
  }
  </style>