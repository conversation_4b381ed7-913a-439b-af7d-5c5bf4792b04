<template>
  <div class="list-box">
    <div class="list-box-head-r">
      <div class="list-box-head-r-export">
        <el-tooltip content="到“下载列表”的“日志导出”中下载" placement="top" effect="dark">
          <el-button @click="httpDerive">
            <svg-icon icon-class="globaldown" style="margin-right: 5px" />日志导出
          </el-button>
        </el-tooltip>
      </div>
      <div class="list-box-head-r-JH">
        <dialogueListFliter ref="header" @checkedHeaders1="checkedHeaders"></dialogueListFliter>
      </div>
    </div>
    <div class="list-box-from">
      <el-table ref="httplist" :data="tableData" stripe
                border @selection-change="handleSelectionChange" @sort-change="handleSortChange"
      >
        >
        <el-table-column prop="num" label="序号" type="index" width="100" :index="indexMethod" fixed />
        <el-table-column property="sIp" label="客户端IP">
          <!-- <template slot-scope="scope">
            <span
              @click="opneipDetail(scope.row.sIp)"
              style="cursor: pointer; color: #116ef9"
              >{{ scope.row.sIp }}</span
            >
          </template> -->
          <template slot-scope="scope">
            <div class="sortbox">
              <div class="top" style="cursor: pointer; color: #116ef9" @click="opneipDetail(scope.row.dIp,0)">
                {{ scope.row.sIp }}
              </div>
              <div class="down">
                <div class="sorttoole">
                  <el-popover placement="bottom" width="200" trigger="hover" popper-class="sortpopover1">
                    <span slot="reference" class="sorttoole-r">...</span>

                    <div class="sortbtn">
                      <div class="sortbtn-top" style="position: relative">
                        <svg-icon icon-class="sort-up" class="advanceicon" />
                        <el-button style="width: 100%" @click="forwardsort(scope, 'ip')">IP正向检索</el-button>
                        <el-button @click="forwardsort(scope, 'dip')">服务端IP正向检索</el-button>
                        <el-button @click="forwardsort(scope, 'sip')">客户端IP正向检索</el-button>
                      </div>
                      <div class="sortbtn-down" style="position: relative">
                        <svg-icon icon-class="sort-down" class="advanceicon" />
                        <div class="title">IP反向检索</div>
                        <el-button @click="reversesort(scope, 'dip')">服务端IP反向检索</el-button>
                        <el-button @click="reversesort(scope, 'sip')">客户端IP反向检索</el-button>
                      </div>
                    </div>
                  </el-popover>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column property="dIp" label="服务端IP">
          <!-- <template slot-scope="scope">
            <span @click="opneipDetail(scope.row.dIp)" style="cursor: pointer; color: #116ef9">{{ scope.row.dIp
            }}</span>
          </template> -->
          <template slot-scope="scope">
            <div class="sortbox">
              <div class="top" style="cursor: pointer; color: #116ef9" @click="opneipDetail(scope.row.dIp,0)">
                {{ scope.row.dIp }}
              </div>
              <div class="down">
                <div class="sorttoole">
                  <el-popover placement="bottom" width="200" trigger="hover" popper-class="sortpopover1">
                    <span slot="reference" class="sorttoole-r">...</span>

                    <div class="sortbtn">
                      <div class="sortbtn-top" style="position: relative">
                        <svg-icon icon-class="sort-up" class="advanceicon" />
                        <div class="title">IP正向检索</div>
                        <el-button @click="forwardsort(scope, 'dip')">服务端IP正向检索</el-button>
                        <el-button @click="forwardsort(scope, 'sip')">客户端IP正向检索</el-button>
                      </div>
                      <div class="sortbtn-down" style="position: relative">
                        <svg-icon icon-class="sort-down" class="advanceicon" />
                        <div class="title">IP反向检索</div>
                        <el-button @click="reversesort(scope, 'dip')">服务端IP反向检索</el-button>
                        <el-button @click="reversesort(scope, 'sip')">客户端IP反向检索</el-button>
                      </div>
                    </div>
                  </el-popover>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="应用协议">
          <template>HTTP</template>
        </el-table-column>
        <el-table-column property="dPort" label="服务端端口">
          <template slot-scope="scope">
            <div class="sortbox">
              <div class="top">
                {{ scope.row.dPort }}
              </div>
              <div class="down">
                <div class="sorttoole">
                  <el-popover placement="bottom" width="200" trigger="hover" popper-class="sortpopover">
                    <span slot="reference" class="sorttoole-r">...</span>
                    <div class="sortbtn">
                      <el-button @click="forwardSearch(scope.row, 'dPort')">
                        <svg-icon icon-class="sort-up" style="margin-right: 15px" />正向检索
                      </el-button>
                      <el-button @click="reverseSearch(scope.row, 'dPort')">
                        <svg-icon icon-class="sort-down" style="margin-right: 15px" />反向检索
                      </el-button>
                    </div>
                  </el-popover>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column v-if="checkedHeader.includes('网址(URL)')" property="Url" label="网址(URL)">
          <template slot-scope="scope">
            <div class="sortbox">
              <div class="top">
                {{ scope.row.Url }}
              </div>
              <div class="down">
                <div class="sorttoole">
                  <el-popover placement="bottom" width="200" trigger="hover" popper-class="sortpopover">
                    <span slot="reference" class="sorttoole-r">...</span>
                    <div class="sortbtn">
                      <el-button @click="forwardSearch(scope.row, 'Url')">
                        <svg-icon icon-class="sort-up" style="margin-right: 15px" />正向检索
                      </el-button>
                      <el-button @click="reverseSearch(scope.row, 'Url')">
                        <svg-icon icon-class="sort-down" style="margin-right: 15px" />反向检索
                      </el-button>
                    </div>
                  </el-popover>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column v-if="checkedHeader.includes('主站')" property="Host" label="主站">
          <!-- <template slot-scope="scope">
            <span @click="opneDomainDetail(scope.row.Host)" style="cursor: pointer; color: #116ef9">{{ scope.row.Host
            }}</span>
          </template> -->
          <template slot-scope="scope">
            <div class="sortbox">
              <div class="top">
                <span style="color: #116ef9" :style="
                  ipverify_http(scope.row.Host).sign
                    ? 'cursor: not-allowed'
                    : 'cursor: pointer'
                " @click="
                  ipverify_http(scope.row.Host).sign
                    ? ''
                    : opneDomainDetail(scope.row.Host,3)
                "
                >
                  {{ ipverify_http(scope.row.Host).host }}
                </span>
              </div>
              <div class="down">
                <div class="sorttoole">
                  <el-popover placement="bottom" width="200" trigger="hover" popper-class="sortpopover">
                    <span slot="reference" class="sorttoole-r">...</span>
                    <div class="sortbtn">
                      <el-button @click="forwardSearch(scope.row, 'HTTP.Host')">
                        <svg-icon icon-class="sort-up" style="margin-right: 15px" />正向检索
                      </el-button>
                      <el-button @click="reverseSearch(scope.row, 'HTTP.Host')">
                        <svg-icon icon-class="sort-down" style="margin-right: 15px" />反向检索
                      </el-button>
                    </div>
                  </el-popover>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <!-- <el-table-column property="sHTTPFinger" label="源端HTTP指纹(HTTP-UA)"
          v-if="checkedHeader.includes('源端HTTP指纹(HTTP-UA)')">

          <template slot-scope="scope">
            <div class="sortbox">
              <div class="top">
                {{ scope.row.sHTTPFinger }}
              </div>
              <div class="down">
                <div class="sorttoole">
                  <el-popover placement="bottom" width="200" trigger="hover" popper-class="sortpopover">
                    <span class="sorttoole-r" slot="reference">...</span>
                    <div class="sortbtn">
                      <el-button @click="forwardSearch(scope.row, 'sHTTPFinger')">
                        <svg-icon icon-class="sort-up" style="margin-right: 15px" />正向检索
                      </el-button>
                      <el-button @click="reverseSearch(scope.row, 'sHTTPFinger')">
                        <svg-icon icon-class="sort-down" style="margin-right: 15px" />反向检索
                      </el-button>
                    </div>
                  </el-popover>
                </div>
              </div>
            </div>
          </template>
        </el-table-column> -->
        <el-table-column v-if="checkedHeader.includes('客户端.客户端信息')" prop="Client.User-Agent"
                         :show-overflow-tooltip="true" label="客户端.客户端信息"
        >
          <template slot-scope="scope">
            <div class="sortbox">
              <div class="top">
                <el-tooltip :disabled="!scope.row['Client.User-Agent']" class="item" effect="light" placement="top"
                            popper-class="sessionidTooltip"
                >
                  <div slot="content">
                    {{ scope.row['Client.User-Agent'] }}
                  </div>
                  <div>
                    {{
                      scope.row['Client.User-Agent']
                        ? scope.row['Client.User-Agent'].slice(0, 20) + "..."
                        : "--"
                    }}
                  </div>
                </el-tooltip>
              </div>
              <div class="down">
                <div class="sorttoole">
                  <el-popover placement="bottom" width="200" trigger="hover" popper-class="sortpopover">
                    <span slot="reference" class="sorttoole-r">...</span>
                    <div class="sortbtn">
                      <el-button @click="forwardSearch(scope.row, 'Client.User-Agent')">
                        <svg-icon icon-class="sort-up" style="margin-right: 15px" />正向检索
                      </el-button>
                      <el-button @click="reverseSearch(scope.row, 'Client.User-Agent')">
                        <svg-icon icon-class="sort-down" style="margin-right: 15px" />反向检索
                      </el-button>
                    </div>
                  </el-popover>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column v-if="checkedHeader.includes('元数据_HTTP条数')" property="cnt" label="元数据_HTTP条数"></el-table-column>
      </el-table>
    </div>
    <div v-show="showfoot" class="list-box-foot">
      <div class="list-box-foot-top">
        <tablescroll :table-ref="$refs.httplist"></tablescroll>
      </div>
      <div class="list-box-foot-down">
        <div class="list-box-foot-down-l">
          *元数据_HTTP上限为<span>10,000</span>条
        </div>
        <div class="list-box-foot-down-r">
          <el-pagination :current-page="currentPage" :page-sizes="[10, 20, 30, 50]" :page-size="page_size"
                         layout="total, sizes, prev, pager, next, jumper" :total="total" @size-change="handleSizeChange"
                         @current-change="handleCurrentChange"
          >
          </el-pagination>
        </div>
      </div>
    </div>
    <!-- ip详情抽屉 -->
    <div class="Drawerbox">
      <ipdetails :ip="ip" :tag_target_type="tag_target_type" :ip-detail-drawer="ipDetailDrawer" @closeDrawer="closeDrawer" />
    </div>
    <!-- Domain域名详情抽屉 -->
    <div class="Drawerbox">
      <Domaindetails :domain-detail-drawer="DomainDetailDrawer"
                     :tag_target_type="tag_target_type"
                     :domain-list="domain" @DomaincloseDrawer="DomaincloseDrawer"
      />
    </div>
  </div>
</template>

<script>
import dialogueListFliter from "./dialogueListFliter.vue";
import {
  getmetadataList,
  alllogderive,
  GetIpInfo,
  GetDomainInfo,
} from "@/api/sessionList/sessionlist";
import { common } from "../listfn/Detailfn";
import tablescroll from "../../../../components/TableScroll/idnex.vue";
import ipdetails from "../../components/details/IPdetails.vue";
import Domaindetails from "../../components/details/Domaindetails.vue";
export default {
  components: {
    dialogueListFliter,
    tablescroll,
    ipdetails,
    Domaindetails,
  },
  // 调用 mixin 将组件js与共用js合并 ---
  mixins: [common],
  props: ["searchData"],
  // -------------------------------
  data() {
    return {
      showfoot: false,
      order: {
        order_prop: "",
        order_field: "",
        asc: true,
      },
      reset_stamp: 0,
      tableData: [],
      total: 0,
      current_page: 1,
      page_size: 10,
      order_field: "",
      asc: true,
      currentPage: 1,
      page_size: 10,
      checkedHeader: [],
      display: "none", //json框
      session_id: "", //json框
      filterData: {},
      sessionParam: {},
      dialoguenamearr: ["sIp", "dPort", "dIp", "Url", "Host", "Client.User-Agent"],
      exArr:[
        {
          name:'客户端IP',
          val:'sIp'
        },
        {
          name:'服务端IP',
          val:'dIp'
        },
        {
          name:'应用协议',
          val:'appName'
        },
        {
          name:'服务端端口',
          val:'dPort'
        },
        {
          name:'网址(URL)',
          val:'Url'
        },
        {
          name:'主站',
          val:'Host'
        },
        {
          name:'客户端.客户端信息',
          val:'Client.User-Agent'
        },
        {
          name:'元数据_HTTP条数',
          val:'cnt'
        }
      ],
      listdata: {},
      // 控制抽屉出现位置
      direction: "rtl",
      // ip详情抽屉
      ipDetailDrawer: false,
      ip: '',
      // Domain详情抽屉
      DomainDetailDrawer: false,
      domain:'',
      tag_target_type:9999
    };
  },
  watch: {
    searchData: {
      handler(val) {
        console.log(val, "httphttphttphttphttphttphttphttphttp");
        let value = JSON.parse(JSON.stringify(val));
        this.current_page = 1;
        if (JSON.stringify(value) == "{}") {
          //  console.log('kong')
        } else {
          //  console.log('value',value)
          this.listdata = value;
          this.initData(value);
        }
      },
      deep: true,
      // 配置立即执行属性
      immediate: true,
    },
  },
  mounted() {
    this.$refs.header.inittable();
  },
  methods: {
    httpDerive() {
      let tempArr = [];
      for (let i of this.checkedHeader) {
        console.log(i);
        for (let j of this.exArr) {
          if (i === j.name && j.val != 'cnt') {
            tempArr.push(j.val);
          }
        }
      }
      this.dialoguenamearr = tempArr;
      let param = {
        condition: {
          current_page: this.currentPage,
          page_size: this.page_size,
          order_field: "CreateTime",
          asc: this.order.asc,
          query: this.listdata.query,
          field_names: this.dialoguenamearr,
          data_key: "HTTP",
        },
        user_id: 1,
        task_type: 5,
      };
      if (JSON.stringify(this.filterData) != "{}") {
        let arr = [];
        for (let i in this.filterData) {
          arr.push(this.filterData[i]);
        }
        param.condition.query = param.condition.query.concat(arr);
      }
      if (this.listdata.query.length > 0) {
        param.condition.query = param.condition.query.concat(
          this.listdata.query
        );
      }
      this.listdata.task_id
        ? (param.condition.task_id = this.listdata.task_id)
        : null;
      this.listdata.time_range
        ? (param.condition.time_range = this.listdata.time_range)
        : null;
      let arr = {
        and: [],
        not: []
      };
      for (let i = 0; i < param.condition.query.length; i++) {
        for (let j = 0; j < param.condition.query[i].search.length; j++) {
          if (param.condition.query[i].bool_search === 'and') {
            if (param.condition.query[i].search[j].target === 'Labels') {
              arr.and.push(param.condition.query[i].search[j].val);
            }
          }
          if (param.condition.query[i].bool_search === 'not') {
            if (param.condition.query[i].search[j].target === 'Labels') {
              arr.not.push(param.condition.query[i].search[j].val);
            }
          }
        }
      }
      param.condition.tag_query = arr;
      alllogderive(param).then((res) => {
        console.log(res);
        if (res.err == 0) {
          this.$store.commit("conversational/setdowndot", true);
          this.$message.success("成功导出");
        } else {
          this.$message(res.msg);
        }
      });
    },
    checkedHeaders(data) {
      this.checkedHeader = data;
    },
    indexMethod(index) {
      return (this.current_page - 1) * this.page_size + index + 1;
    },
    handleSizeChange(val) {
      // console.log(val);
      this.page_size = val;
      this.page_size = val;
      this.initData(this.listdata);
    },
    handleSelectionChange(val) {
      this.checkConfigList = val;
    },
    handleSortChange({ column, prop, order }) {
      this.order.order_prop = prop;
      this.asc = order === "descending" ? false : true;
      this.initData(this.listdata);
    },
    handleCurrentChange(val) {
      this.current_page = val;
      this.currentPage = val;
      this.initData(this.listdata);
    },
    ipFilterHandler(prop, checked_filters, sort, checkAll, search_filter) {
      // console.log(prop, checked_filters, sort, checkAll, search_filter);
      let ppid = [];
      for (let i in checked_filters) {
        ppid.push(String(checked_filters[i]));
      }
      this.filterData[prop] = {
        bool_search: "and",
        search: [
          {
            target:
              prop === "src_ip" ? "sIp" : prop === "dst_ip" ? "dIp" : "Tag",
            val: ppid,
          },
        ],
      };
      this.order.order_prop = prop;
      this.order.asc = sort;
      this.fuzzy_match = null;
      switch (prop) {
      case "src_ip":
        this.order.order_field = "sIp";
        break;
      case "dst_ip":
        this.order.order_field = "dIp";
        break;
        // case 'tags':
        //   this.order.order_prop = 'begin_time';
        //   this.order.order_field = 'StartTime';
        //   this.order.asc = true;
        //   break;
      }
      if (sort === null) {
        this.order.order_prop = "";
        this.order.order_field = "";
        this.order.asc = null;
      }
      if (checkAll) {
        this.fuzzy_match = {};
        this.fuzzy_match[prop] = search_filter;
      }
      (this.current_page = 1), (this.page_size = 10), this.initData();
    },
    initData(val) {
      let param = {
        // "top": this.top,
        current_page: this.current_page,
        page_size: this.page_size,
        order_field: this.order.order_prop,
        asc: this.order.asc,
        query: [],
        task_id: this.listdata.task_id,
        field_names: this.dialoguenamearr,
        data_key: "HTTP",
        time_range: val?.time_range,
      };
      if (JSON.stringify(this.filterData) != "{}") {
        let arr = [];
        for (let i in this.filterData) {
          arr.push(this.filterData[i]);
        }
        param.query = param.query.concat(arr);
      }
      if (this.listdata.query?.length > 0) {
        param.query = param.query.concat(this.listdata.query);
      }
      param.aggr_query = false;
      // for (let i of param.query) {
      //   for (let j of i.search) {
      //     if (j.target === 'Labels') {
      //       param.aggr_query = true
      //     }
      //   }
      // }
      let arr = {
        and: [],
        not: []
      };
      for (let i = 0; i < param.query.length; i++) {
        for (let j = 0; j < param.query[i].search.length; j++) {
          if (param.query[i].bool_search === 'and') {
            if (param.query[i].search[j].target === 'Labels') {
              arr.and.push(param.query[i].search[j].val);
            }
          }
          if (param.query[i].bool_search === 'not') {
            if (param.query[i].search[j].target === 'Labels') {
              arr.not.push(param.query[i].search[j].val);
            }
          }
        }
      }
      param.tag_query = arr;
      this.sessionParam = param;
      getmetadataList(param).then((res) => {
        if (res.err === 0) {
          this.tableData = res.data.records;
          if (res.data.records.length != 0) {
            this.showfoot = true;
          }
          // for (let i in this.tableData) {
          //   for (let I in this.tableData[i]) {
          //     if (I === "Client.User-Agent") {
          //       let item = {
          //         UserAgent: this.tableData[i]['Client.User-Agent'],
          //       };
          //       Object.assign(this.tableData[i].Client, item);
          //     }
          //   }
          // }
          // console.log(this.tableData,"))))))))))))))))))))0")

          this.total = res.data.total;
          this.$emit("update:fatherValue", res.data.total);
        } else {
          this.$message.error(res.msg);
          this.total = 0;
          this.$emit("update:fatherValue", 0);
        }
      });
    },
    // 打开ip详情
    opneipDetail(ip,type) {
      this.ip=ip;
      this.ipDetailDrawer = true;
      this.tag_target_type=type;
    },
    closeDrawer() {
      this.ipDetailDrawer = false;
    },

    // 打开域名详情
    opneDomainDetail(domain,type) {
      this.domain=this.DomaininfoDandle(domain); 
      this.tag_target_type=type;
      this.DomainDetailDrawer = true;
    },
    DomaincloseDrawer() {
      this.DomainDetailDrawer = false;
    },
    // 客户端IP正向检索
    forwardsort(data, sign) {
      if (sign == "ip") {
        data.row.sort = false;
        data.row.sortname = sign;

        this.$store.commit("conversational/getSessionQuickSearch", data.row);
      }
      if (sign == "sip") {
        data.row.sort = false;
        data.row.sortname = sign;
        data.row.src_ip = data.row.sIp;
        this.$store.commit("conversational/getSessionQuickSearch", data.row);
      }
      if (sign == "dip") {
        data.row.sort = false;
        data.row.sortname = sign;
        data.row.dst_ip = data.row.dIp;
        this.$store.commit("conversational/getSessionQuickSearch", data.row);
      }
    },
    // 客户端IP反向检索
    reversesort(data, sign) {
      if (sign == "ip") {
        data.row.sort = true;
        data.row.sortname = sign;
        this.$store.commit("conversational/getSessionQuickSearch", data.row);
      }
      if (sign == "sip") {
        data.row.sort = true;
        data.row.sortname = sign;
        data.row.src_ip = data.row.sIp;
        this.$store.commit("conversational/getSessionQuickSearch", data.row);
      }
      if (sign == "dip") {
        data.row.sort = true;
        data.row.sortname = sign;
        data.row.dst_ip = data.row.dIp;
        this.$store.commit("conversational/getSessionQuickSearch", data.row);
      }
    },
    // 正向快速检索
    forwardSearch(data, sign) {
      console.log(data);
      console.log(sign);
      data.sort = false;
      data.sortname = sign;
      data.AppName = data.appName;
      data.dst_port = data.dPort;
      data.Host = data.Host;
      this.$store.commit("conversational/getSessionQuickSearch", data);
    },
    // 反向快速检索
    reverseSearch(data, sign) {
      data.sort = true;
      data.sortname = sign;
      data.AppName = data.appName;
      data.dst_port = data.dPort;
      data.Host = data.Host;
      this.$store.commit("conversational/getSessionQuickSearch", data);
    },
  },
};
</script>

<style lang="scss" scoped>
.list-box {
  &-head-r {
    margin-bottom: 20px;
    display: flex;
    justify-content: flex-end;
    align-items: center;

    .el-button {
      display: flex;
      justify-content: center;
      align-items: center;
    }

    &-export {
      margin-right: 8px;

      .el-button {
        width: 108px;
        height: 32px;
      }
    }

    &-JH {
      .el-button {
        width: 94px;
        height: 32px;
      }
    }
  }

  &-from {
    margin-top: 10px;

    .sortbox {
      display: flex;
      justify-content: flex-start;
      align-items: center;

      // position: relative;
      .top {
        margin-right: 5px;
      }

      .down {
        width: 10px;
        height: 20px;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .sorttoole {
        display: none;
        padding-bottom: 5px;
        color: #116ef9;
        // position: absolute;
        // top: -2px;
        // right: 0;
      }
    }

    .sortbox:hover {
      .sorttoole {
        display: block;
        cursor: pointer;
      }
    }
  }

  &-foot {
    z-index: 999;
    padding: 10px 0;
    position: sticky;
    // right: 34px;
    bottom: 0px;
    width: 100%;
    background: #ffffff;
    border-radius: 8px;
    display: flex;
    flex-direction: column;

    &-top {
      margin-bottom: 5px;
    }

    &-down {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;

      // margin-bottom: 20;
      &-l {
        font-size: 12px;
        color: #9999a1;

        span {
          color: #000;
        }
      }
    }
  }

  // .Drawerbox {
  //   ::v-deep {
  //     .el-drawer {
  //       // width: 480px !important;
  //       overflow: auto;
  //     }
  //   }
  // }
}
</style>