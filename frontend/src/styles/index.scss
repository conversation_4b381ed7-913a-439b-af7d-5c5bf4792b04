@import './variables.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';

@font-face {
  font-family: "alpht";
  src: url('../assets/Alibaba-PuHuiTi-Regular.ttf');
  font-weight: normal;
}
body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  // font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
  // font-family: Microsoft YaHei;
  font-family: "alpht";
  padding-right: 0px !important;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
  overflow-y: hidden;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

// main-container global css
.app-container {
  // padding: 10px;
}

scrollbar {
  width: 4px;
  height: 4px;
  // display: none;
}

::-webkit-scrollbar {
  width: 4px;
  height: 4px;
  // display: none;

}

scrollbar-thumb {
  background-color: #e7e7e7;
  border-radius: 3px;
  // display: none;

}

/* 滚动条的滑块 */
::-webkit-scrollbar-thumb {
  background-color: #e7e7e7;
  border-radius: 3px;
  // display: none;

}

// div,
// article,
// section {
//   transition: width 1.5s cubic-bezier(0.18, 0.89, 0.32, 1.28)

// }
.leader-line{
  z-index: 699;
}
/* 活动页码按钮背景色 */
.el-pagination.is-background .el-pager li.active {
  background-color: #116EF9 !important;
  color: #ffffff;
}


/* tabs颜色 */
.el-tabs__item.is-active{
  color:#116ef9;
}
.el-tabs__item_active{
  color:#116ef9;
}
.el-tabs__item:hover{
  color:#116ef9;
}
.el-tabs__active-bar{
background-color:#116ef9 ;
}