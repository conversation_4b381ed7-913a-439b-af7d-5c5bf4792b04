// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

// 按钮设置
.el-button {
  // width: 120px;
  // border: 1px solid #8ABCFF;
  // color: #4A97FF;
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block
  }
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

// table header
.el-table__header-wrapper {
  border-radius: 4px;
  // height: 27px;

  .el-table__header {
    height: 100%;

    th,
    tr {
      background-color: #F2F7FF !important;
      font-size: 14px;
      color: #1B428D;
      padding-top: 6px;
      padding-bottom: 6px;
      font-weight: 500;
      // text-align: center;
    }
  }
}

.el-table__fixed {
  height: 100% !important;


  .el-table__header {

    td,
    th {
      background-color: #F2F7FF !important;
      margin: 0;
      font-weight: 500;
      font-size: 14px;
      color: #1B428D;
      // text-align: center;
      padding: 6px 0;

    }
  }
}

.el-table__fixed-right {
  height: 100% !important;

  .el-table__header {

    td,
    th {
      background-color: #E7F0FE;
      font-weight: 500;
      font-size: 14px;
      color: #1B428D;
      // text-align: center;
      padding: 6px 0;
      // background-color: #E7F0FE
    }
  }

}

.el-table--striped .el-table__body tr.el-table__row--striped td {
  background: #f7f8fa;
}



.el-table__body tr.hover-row>td {
  background-color: #F5F7FF !important;
}

.el-table__body {

  tr,
  td {
    // text-align: center;
    font-size: 14px;
    color: #000000;
    // border-color: #116EF9;
    border-bottom: none;
    border-top: none;
  }
}

.el-table--scrollable-x .el-table__body-wrapper {
  overflow-x: hidden;
}

// =================================

::-webkit-scrollbar {
  height: 10px !important;
  width: 10px !important;
}

::-webkit-scrollbar-thumb {
  border-radius: 10px !important;
  border-style: dashed !important;
  border-color: transparent !important;
  border-width: 2px !important;
  background-color: rgba(157, 165, 183, 0.7) !important;
  background-clip: padding-box !important;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(157, 165, 183, 1) !important;

  cursor: pointer !important;
}

.alldownbox {
  // width: 80px !important;
  // left: 1155px !important;
  padding: 8px 0 !important;

  .alldownfoot {
    display: flex;
    flex-direction: column;
  }

  .el-button {
    // width: 120px;
    margin: 1px 0px;
    border: 0;
  }
}

.sortpopover3 {
  padding: 8px 0 !important;

  .advanceicon {
    margin-right: 15px !important;
    position: absolute !important;
    top: 12px !important;
    left: 4px !important;
  }

  .sortbtn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    &-top {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;


    }

    &-down {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;

      .advanceicon {
        margin-right: 15px;
        position: absolute;
        top: 12px;
        left: 5px;
      }
    }

    .el-button {
      width: 100%;
      border: 0;
      margin: 0 !important;
      // width: 150px;
      // height: 32px;
      display: flex;
      // justify-content: flex-start;
      // align-items: center;
      font-weight: 400;
      font-size: 14px;
    }
  }
}

// 自定义纯白Tooltip
.sessionidTooltip {
  border: 1px solid #ffff !important;
  background: #FFFFFF !important;
  opacity: 0.96 !important;
  box-shadow: 0px 8px 20px rgba(0, 0, 0, 0.1) !important;
  border-radius: 4px !important;
}

.sessionidTooltip .popper__arrow {
  border-top-color: #FFFFFF !important;

}

.sortpopover {
  padding: 8px 0 !important;
  width: 60px !important;

  .sortbtn {
    display: flex;
    flex-direction: column;
    // align-items: center;
    // justify-content: center;

    .el-button {
      border: 0;
      margin: 0 !important;
      // width: 150px;
      // height: 32px;
      // display: flex;
      // justify-content: flex-start;
      // align-items: center;
      font-weight: 400;
      font-size: 14px;
    }
  }
}

.sortpopover1 {
  padding: 8px 0 !important;

  width: 155px !important;

  .advanceicon {
    margin-right: 15px !important;
    position: absolute !important;
    top: 12px !important;
    left: 4px !important;
  }

  .sortbtn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    &-top {
      display: flex;
      flex-direction: column;
      align-items: center;

      .title {
        width: 100%;
        padding: 12px 20px;
        color: #000000;
      }
    }

    &-down {
      display: flex;
      flex-direction: column;
      align-items: center;

      .title {
        width: 100%;
        padding: 12px 20px;
        color: #000000;
      }
    }

    .el-button {
      border: 0;
      margin: 0 !important;
      // width: 150px;
      // height: 32px;
      display: flex;
      justify-content: flex-start;
      // align-items: center;
      font-weight: 400;
      font-size: 14px;
    }
  }
}

.el-pagination button,
.el-pagination span:not([class*=suffix]) {
  font-size: 14px;
}

.el-pagination__total {
  color: #767684;
}

.el-pagination__sizes {
  color: #767684;
}

.el-button--primary {
  background: #116EF9;
}

.el-switch.is-checked .el-switch__core {
  border-color: #116EF9;
  background-color: #116EF9;
}

.el-switch__core {
  width: 35px !important;
}

.el-tag {
  padding: 0 4px !important;
}

.el-tag .el-icon-close {
  right: -1px;
}

.el-drawer__header {
  color: #2c2c35;
  margin: 0;
  font-size: 14px;
  padding: 12px 20px 10px;
  border-bottom: 1px solid #F2F3F7;
}

.el-tooltip__popper.is-dark {
  border: 0;

  .dom-content {
    margin-right: 4px;
  }

  .dom-copy {
    cursor: pointer;
    color: #4A97FF;
  }

  .dom-copy:hover {
    text-decoration: underline
  }
}

.el-input__inner {
  height: 32px;
  padding-right: 2px;
  border-color: #cecece;
}

.el-input__inner:hover {
  border-color: #116ef9;
}

.el-radio__input.is-checked .el-radio__inner {
  border-color: #116ef9;
  background: #116ef9;
}

// 时间选择器
.timebox {
  .el-picker-panel__footer {
    .el-button {
      padding: 7px 15px;
    }

    .el-button--text:hover {
      border: 1px solid #8ABCFF;
    }
  }

}

// 遮罩
.el-drawer__mask {
  background-color: #000000;
}

.el-pagination {
  margin-top: 12px;
  text-align: right;
}

.drawer-detail {
  font-size: 14px;
  padding-bottom: 56px;

  .el-drawer__header {
    margin-bottom: 0px;
    padding: 0px;
    text-indent: 16px;
    height: 56px;
    border-bottom: 1px solid #f2f3f7;
    line-height: 56px;
    color: #2c2c35;
    font-weight: 600;
  }

  .drawer-detail__content {
    padding: 16px;

    .detail-label {
      width: 100%;
      height: 22px;
      line-height: 22px;
      color: #2c2c35;
      font-size: 14px;
      font-weight: 600;
      margin-top: 24px;
      margin-bottom: 8px;
    }
  }

  .drawer-detail__footer {
    width: 100%;
    height: 56px;
    line-height: 56px;
    border-top: 1px solid #f2f3f7;
    position: absolute;
    left: 0;
    bottom: 0;
    padding: 0 16px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .el-button {
      // padding: 0;
      height: 32px;
    }
  }
}

/* tag */
.el-tag {
  padding: 0 4px;

  &:hover {
    cursor: pointer;
  }
}

.el-tag--light.el-tag--danger {
  background: #FCE7E7;
  border-color: #FCE7E7;
  color: #A41818;
}

.el-tag--dark.el-tag--danger {
  background: #FF4848;
  border-color: #FF4848;
  color: #FFFFFF;
}

.el-tag--light.el-tag--warning {
  background: #F9EDDF;
  border-color: #F9EDDF;
  color: #B76F1E;
}

.el-tag--dark.el-tag--warning {
  background: #FFAA5A;
  border-color: #FFAA5A;
  color: #FFFFFF;
}

.el-tag--light.el-tag--success {
  background: #E0F5EE;
  border-color: #E0F5EE;
  color: #006157;
}

.el-tag--dark.el-tag--success {
  background: #2BE0B6;
  border-color: #2BE0B6;
  color: #FFFFFF;
}

.el-tag--light.el-tag--positive {
  background: #E7F0FE;
  border-color: #E7F0FE;
  color: #1B428D;
}

.el-tag--dark.el-tag--positive {
  background: #116EF9;
  border-color: #116EF9;
  color: #FFFFFF;
}

.el-tag--light.el-tag--info {
  background: #DEE0E7;
  border-color: #DEE0E7;
  color: #2C2C35;
}

.el-tag--dark.el-tag--info {
  background: #767684;
  border-color: #767684;
  color: #FFFFFF;
}
