<template>
  <div>
    <!-- 第二步 -->
    <div class="step2" v-if="step2data">
      <el-dialog
        :visible.sync="step2data"
        width="30%"
        @close="closedialog"
        :close-on-click-modal="false"
      >
        <template slot="title">
          <div class="step2-title">
            <div>
              <svg-icon icon-class="icon-gray-hint" />
            </div>
            提醒
          </div>
        </template>
        <div class="step2-main">
          <div class="step2-main-text">
            若数据盘读取成功，<span>机器将自动重启以激活所有服务，</span>过程大约需要5分钟。确定要重组数据盘吗？<br />
            <!-- <span style="color: #f91111"
              >置换旧硬盘需要做两次重组，请熟知！</span
            > -->
          </div>
          <div class="step2-main-foot">
            <div>
              <el-button @click="laststep">上一步</el-button>
            </div>
            <div class="btn">
              <el-button @click="closedialog">取消</el-button>
              <el-button
                style="background: #116ef9; color: #ffffff"
                @click="fnReset"
                >确认</el-button
              >
            </div>
          </div>
        </div>
      </el-dialog>
    </div>
    <!-- 第三步 -->
    <div class="step4" v-if="step4">
      <el-dialog
        :visible.sync="step4"
        width="35%"
        :close-on-click-modal="false"
        :show-close="false"
      >
        <div class="step4-main">
          <img
            src="../../../../assets/images/Ellipse 279.png"
            alt=""
            style="margin-right: 20px"
          />
          <span>数据盘正在读取中，大约需要10分钟</span>
        </div>
        <span style="margin-left: 50px"
          >若数据盘读取成功，机器将自动重启以激活所有服务。</span
        >
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { readydiskstart } from "@/api/SystemData/systemdata";
let resetnum = 0;
export default {
  props: {
    Readrecom: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    Readrecom: {
      handler(val, old) {
        console.log(val);
        this.step2data = val;
      },
    },
  },
  data() {
    return {
      step2data: false,
      step4: false,
    };
  },

  methods: {
    // 立即重置
    fnReset() {
      this.step4 = true;
      readydiskstart().then((res) => {
        console.log(res);
        resetnum++;
        this.$store.commit("ifonconduct/resetNumber", resetnum);
      });
      this.closestep4();
    },
    // 关闭回调
    closedialog() {
      this.$store.commit("ifonconduct/Readdiskdata", false);
      this.$emit("setHDDrecomdata", false);
    },
    closestep4() {
      console.log("进入到重启提示中");
      setTimeout(() => {
        this.step2data = false;
        this.step4 = false;
      }, 3000);
      this.$emit("setReadrecomdata", false);
    },
    // 上一步
    laststep() {
      //   this.step2data = false;
      this.$emit("setReadrecomdata", false);
      this.$store.commit("ifonconduct/Readdiskdata", true);
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
}
.step2 {
  ::v-deep {
    .el-dialog__body {
      padding: 0;
    }
  }
  &-title {
    display: flex;
    align-items: center;
    font-size: 16px;
    div {
      margin-right: 8px;
    }
  }

  &-main {
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    &-text {
      padding: 0 24px;
      padding-top: 8px;
      margin-bottom: 30px;
      font-family: "Alibaba PuHuiTi";
      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
      color: #2c2c35;
      span {
        font-weight: bold;
      }
    }
    &-foot {
      border-top: 1px solid #2d2f3324;
      width: 100%;
      height: 50px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      div:nth-child(1) {
        ::v-deep {
          .el-button {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 74px;
            height: 32px;
          }
        }
        margin-left: 24px;
      }
      .btn {
        margin-right: 24px;
        ::v-deep {
          .el-button {
            height: 32px !important;
            line-height: 0;
          }
        }
      }
    }
  }
}
.step4 {
  span {
    font-family: "Alibaba PuHuiTi";
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    color: #9999a1;
  }
  &-main {
    display: flex;
    align-items: center;
    span {
      font-family: "Alibaba PuHuiTi";
      font-style: normal;
      font-weight: 500;
      font-size: 16px;
      color: #2c2c35;
    }
    img {
      margin-top: 10px;
      margin-right: 20px;
    }
  }
  ::v-deep {
    .el-dialog__body {
      padding: 0;
      padding-top: 36px;
      padding-left: 32px;
      padding-bottom: 40px;
    }
    .el-dialog__header {
      padding: 0;
    }
  }
}
</style>