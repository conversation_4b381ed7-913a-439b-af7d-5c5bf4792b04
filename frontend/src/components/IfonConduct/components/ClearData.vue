<template>
  <div>
    <div v-if="step2data" class="step2">
      <el-dialog
        title="数据清理"
        :visible.sync="step2data"
        width="600px"
        :close-on-click-modal="false"
        @close="closedialog"
      >
        <div class="clearlist">
          <div class="clearlist-top">
            <div>
              <el-checkbox
                v-model="checkAll"
                :indeterminate="isIndeterminate"
                @change="handleCheckAllChange"
              >
                主任务
              </el-checkbox>
            </div>
            <el-checkbox-group
              v-model="checkedCities"
              @change="handleCheckedCitiesChange"
            >
              <div class="checkbox">
                <el-checkbox v-for="item in rules" :key="item" :label="item">
                  {{
                    item
                  }}
                </el-checkbox>
              </div>
            </el-checkbox-group>
          </div>
          <div class="clearlist-mid">
            <div>
              <el-checkbox
                v-model="checkAll2"
                :indeterminate="isIndeterminate2"
                @change="handleCheckAllChange2"
              >
                从任务
              </el-checkbox>
            </div>
            <el-checkbox-group
              v-model="checkedCities2"
              @change="handleCheckedCitiesChange2"
            >
              <div class="checkbox">
                <el-checkbox v-for="item in rules2" :key="item" :label="item">
                  {{
                    item
                  }}
                </el-checkbox>
              </div>
            </el-checkbox-group>
          </div>
          <!-- <div class="clearlist-foot">
            <el-checkbox v-model="checked">证书文件</el-checkbox>
          </div> -->

          <div class="clearlist-foot">
            <el-checkbox v-model="checked" @change="certfn">
              证书文件
            </el-checkbox>
          </div>
        </div>
        <div class="btnbox">
          <div>
            <el-button @click="laststep">上一步</el-button>
          </div>
          <div class="btn">
            <el-button @click="closedialog">取消</el-button>
            <el-button class="r" type="danger" @click="fnReset">
              立即清理
            </el-button>
          </div>
        </div>
      </el-dialog>
    </div>
    <!-- 第三步 -->
    <div class="step3">
      <el-dialog
        title="数据清理中"
        :visible.sync="step3"
        width="30%"
        :close-on-click-modal="false"
        :show-close="false"
        @close="closedialog"
        @open="openstep3"
      >
        <div class="stepbox">
          <div class="title">主任务</div>
          <div v-for="item in steparr" :key="item.id" class="one">
            <div class="onebox">
              <div v-if="!item.check" style="margin-right: 4px" class="imgbox">
                <img
                  src="../../../assets/images/waiting.png"
                  alt=""
                  srcset=""
                  class="imgitem"
                />
              </div>
              <div v-if="item.check" style="margin-right: 4px">
                <img
                  src="../../../assets/images/ready.png"
                  alt=""
                  srcset=""
                  class="imgitem"
                />
              </div>
              <span :class="item.check ? '' : 'nochenk'">{{ item.text }} </span>
            </div>
          </div>
          <div class="title">从任务</div>
          <div v-for="item in steparr2" :key="item.text" class="two">
            <div v-if="!item.check" style="margin-right: 4px" class="imgbox">
              <img
                src="../../../assets/images/waiting.png"
                alt=""
                srcset=""
                class="imgitem"
              />
            </div>
            <div v-if="item.check" style="margin-right: 4px">
              <img
                src="../../../assets/images/ready.png"
                alt=""
                srcset=""
                class="imgitem"
              />
            </div>
            <span :class="item.check ? '' : 'nochenk'">{{ item.text }} </span>
          </div>
        </div>
      </el-dialog>
    </div>
    <!-- 第四步 -->
    <div v-if="step4" class="step4">
      <el-dialog
        :visible.sync="step4"
        width="30%"
        :close-on-click-modal="false"
        @close="closestep4"
      >
        <div class="step4-main">
          <img
            src="../../../assets/images/succeed.png"
            alt=""
            style="margin-right: 20px"
          />
          <span>数据清理完毕。</span>
        </div>
        <div slot="footer" class="step4-foot">
          <el-button @click="closestep4">关闭</el-button>
        </div>
        <!-- <span style="margin-left: 50px">大约需要5分钟</span> -->
      </el-dialog>
    </div>
    <!-- 数组清理失败 -->
    <div v-if="step5" class="step4">
      <el-dialog
        :visible.sync="step5"
        width="30%"
        :close-on-click-modal="false"
      >
        <div class="step4-main">
          <img
            src="../../../assets/images/hint.png"
            alt=""
            style="margin-right: 20px; width: 24px; height: 24px"
          />
          <span>数据清理未完成！</span>
        </div>
        <div slot="footer" class="step4-foot">
          <el-button class="step4-foot-btn" @click="fnReset">
            再次清理
          </el-button>
          <el-button
            style="background: #116ef9; color: #ffffff"
            @click="closestep4"
          >
            关闭
          </el-button>
        </div>
        <!-- <span style="margin-left: 50px">大约需要5分钟</span> -->
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { cleandata, cleandataSchedule } from "@/api/SystemData/systemdata";
const ruleOptions = [
  "任务配置",
  "过滤规则",
  "特征规则",
  "日志信息",
  "pcap数据",
  "会话元数据和协议元数据",
];
export default {
  props: {
    clearstep2: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      updata0: {
        task_id: 0,
        user_id: 0,
        clean_list: [],
      },
      updata1: {
        task_id: 1,
        user_id: 1,
        clean_list: [],
      },
      activeName: "first",
      // 主任务
      isIndeterminate: true,
      checkedCities: [
        "任务配置",
        "日志信息",
        "pcap数据",
        "会话元数据和协议元数据",
      ],
      checkedCitiesdata: [
        { name: "任务配置", val: "conf" },
        { name: "日志信息", val: "log" },
        { name: "pcap数据", val: "pcap" },
        { name: "过滤规则", val: "filter" },
        { name: "特征规则", val: "rule" },
        { name: "会话元数据和协议元数据", val: "PbSession" },
      ],
      checkAll: false,
      rules: ruleOptions,
      // ====================
      // 从任务
      isIndeterminate2: true,
      checkedCities2: [
        "任务配置",
        "日志信息",
        "pcap数据",
        "会话元数据和协议元数据",
      ],
      checkAll2: false,
      rules2: ruleOptions,
      // =====================
      // cert 证书文件
      checked: false,
      // =================
      step2data: false,
      step3: false,
      step4: false,
      step5: false,
      steparr: [
        {
          id: 1,
          text: "清理任务配置",
          check: false,
          time: 100,
          name: "clean_task_conf",
        },
        {
          id: 3,
          text: "清理过滤规则",
          check: false,
          time: 100,
          name: "clean_filter",
        },

        {
          id: 5,
          text: "清理特征规则",
          check: false,
          time: 100,
          name: "clean_rule",
        },

        {
          id: 7,
          text: "清理日志信息",
          check: false,
          time: 100,
          name: "clean_log",
        },
        {
          id: 8,
          text: "清理pcap数据",
          check: false,
          time: 100,
          name: "clean_pcap",
        },
        {
          id: 9,
          text: "会话元数据和协议元数据",
          check: false,
          time: 100,
          name: "clean_proto_data",
        },
        {
          id: 10,
          text: "清理证书数据",
          check: false,
          time: 100,
          name: "clean_cert_data",
        },
      ],
      steparr2: [
        {
          id: 1,
          text: "清理任务配置",
          check: false,
          time: 100,
          name: "clean_task_conf",
        },
        {
          id: 3,
          text: "清理过滤规则",
          check: false,
          time: 100,
          name: "clean_filter",
        },

        {
          id: 5,
          text: "清理特征规则",
          check: false,
          time: 100,
          name: "clean_rule",
        },

        {
          id: 7,
          text: "清理日志信息",
          check: false,
          time: 100,
          name: "clean_log",
        },
        {
          id: 8,
          text: "清理pcap数据",
          check: false,
          time: 100,
          name: "clean_pcap",
        },
        {
          id: 9,
          text: "会话元数据和协议元数据",
          check: false,
          time: 100,
          name: "clean_proto_data",
        },
        {
          id: 10,
          text: "清理证书数据",
          check: false,
          time: 100,
          name: "clean_cert_data",
        },
      ],
      zhu: [],
      fu: [],
      clearplan: {},
      num: 0,
    };
  },
  computed: {
    clearstatus() {
      return this.$store.state.ifonconduct?.clearstatus;
    },
  },
  watch: {
    clearstep2: {
      handler(val, old) {
        this.step2data = val;
        this.initdata();
      },
    },
    clearstatus: {
      handler(val) {
        this.clearplan = val;
        if (val.message?.clean == "do") {
          this.initdata();
          // this.fnReset();
          this.getprogress();
          this.getprogress2();
        } else {
          // this.step4 = true;
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    initdata() {
      let newarr = [];
      for (let i in this.checkedCities) {
        this.checkedCitiesdata.forEach((item) => {
          if (this.checkedCities[i] === item.name) {
            newarr.push(item.val);
          }
        });
      }
      this.updata0.clean_list = newarr;
      this.updata1.clean_list = newarr;
      this.checked = false;
    },
    certfn(e) {
      this.checked = e;
      if (e) {
        this.updata0.clean_list.push("cert");
        this.updata1.clean_list.push("cert");
      } else {
        this.updata0.clean_list.pop();
        this.updata1.clean_list.pop();
      }
    },
    handleCheckAllChange(val) {
      this.checkedCities = val ? ruleOptions : [];
      if (val) {
        this.handleCheckedCitiesChange(ruleOptions);
      } else {
        this.handleCheckedCitiesChange([]);
      }
      this.isIndeterminate = false;
    },
    // 主任务
    handleCheckedCitiesChange(value) {
      let newarr = [];
      for (let i in value) {
        this.checkedCitiesdata.forEach((item) => {
          if (value[i] === item.name) {
            newarr.push(item.val);
          }
        });
      }
      this.updata0.clean_list = newarr;
      if (this.checked) {
        let index = this.updata0.clean_list.indexOf("cert");
        if (index == -1) {
          this.updata0.clean_list.push("cert");
        }
      } else {
        let index = this.updata0.clean_list.indexOf("cert");
        if (index != -1) {
          this.updata0.clean_list.pop("cert");
        }
      }
    },
    handleCheckAllChange2(val) {
      this.checkedCities2 = val ? ruleOptions : [];
      this.isIndeterminate2 = false;
      if (val) {
        this.handleCheckedCitiesChange2(ruleOptions);
      } else {
        this.handleCheckedCitiesChange2([]);
      }
    },
    // 从任务
    handleCheckedCitiesChange2(value) {
      let newarr = [];
      for (let i in value) {
        this.checkedCitiesdata.forEach((item) => {
          if (value[i] === item.name) {
            newarr.push(item.val);
          }
        });
      }
      this.updata1.clean_list = newarr;
      if (this.checked) {
        let index = this.updata1.clean_list.indexOf("cert");
        if (index == -1) {
          this.updata1.clean_list.push("cert");
        }
      } else {
        let index = this.updata1.clean_list.indexOf("cert");
        if (index != -1) {
          this.updata1.clean_list.pop("cert");
        }
      }
    },
    closedialog() {
      this.$store.commit("ifonconduct/getdailogopendata", false);
      this.$emit("setcleardata", false);
    },

    // 立即清理
    async fnReset() {
      window.localStorage.setItem("cleardata", JSON.stringify(this.updata));
      if (
        this.updata0.clean_list.length == 0 &&
        this.updata1.clean_list.length == 0
      ) {
        this.$message.error("请选择要删除的任务配置");
      } else if (this.updata0.clean_list.length == 0) {
        // 从任务清理;
        cleandata(this.updata1).then((res) => {
          if (res.err == 0) {
            // 开始调用获取从任务删除进度
            this.getprogress2(this.updata1.task_id);
          }
        });
      } else if (this.updata1.clean_list.length == 0) {
        // 主任务清理
        cleandata(this.updata0).then((res) => {
          if (res.err == 0) {
            // 开始获主任务的删除进度
            this.getprogress(this.updata0.task_id);
          }
        });
      } else {
        // 主任务清理
        await cleandata(this.updata0).then((res) => {
          if (res.err == 0) {
            // 开始获主任务的删除进度
            this.getprogress(this.updata0.task_id);
          }
        });
        // 从任务清理;
        await cleandata(this.updata1).then((res) => {
          if (res.err == 0) {
            // 开始调用获取从任务删除进度
            this.getprogress2(this.updata1.task_id);
          }
        });
      }
    },

    // 主任务删除进度
    getprogress() {
      let param0 = {
        task_id: this.updata0.task_id,
      };

      cleandataSchedule(param0).then((res) => {
        if (res.err == 0) {
          this.zhu = res.data;
          this.num++;
          this.closedailog();
        }
      });
    },
    // 从任务删除进度
    getprogress2() {
      let param1 = {
        task_id: this.updata1.task_id,
      };

      cleandataSchedule(param1).then((res) => {
        if (res.err == 0) {
          this.fu = res.data;
          this.num++;
          this.closedailog();
        }
      });
    },
    closedailog() {
      if (this.num >= 1) {
        this.$store.commit("ifonconduct/getdailogopendata", false);
        this.step2data = false;
        this.step3 = true;
        this.num = 0;
      }
    },
    async openstep3() {
      let that = this;
      for (let i in this.steparr) {
        that.steparr[i].check = false;
      }
      for (let i in this.steparr2) {
        that.steparr2[i].check = false;
      }
      for (let i = 0; i < this.steparr.length; i++) {
        (function () {
          return new Promise(function () {
            setTimeout(function () {
              for (let v in that.zhu.message) {
                if (v == that.steparr[i].name) {
                  if (that.zhu.message[v] == 0) {
                    that.steparr[i].check = false;
                  }
                  if (that.zhu.message[v] == 2) {
                    that.steparr[i].check = true;
                  }
                }
              }
            }, that.steparr[i].time);
          });
        })();
      }

      for (let i = 0; i < this.steparr2.length; i++) {
        (function () {
          return new Promise(function () {
            setTimeout(function () {
              for (let v in that.fu.message) {
                if (v == that.steparr2[i].name) {
                  if (that.fu.message[v] == 0) {
                    that.steparr2[i].check = false;
                  }
                  if (that.fu.message[v] == 2) {
                    that.steparr2[i].check = true;
                  }
                }
              }

              if (that.steparr2[i].id == 10) {
                setTimeout(() => {
                  that.step2data = false;
                  that.step3 = false;
                  console.log(that.clearplan, "78979878979879787897");
                  if (JSON.stringify(that.clearplan) == "{}") {
                    that.step4 = true;
                  }
                  if (that.clearplan.message.clean == "do") {
                    that.step5 = true;
                    this.step4 = true;
                  }
                }, 6000);
              }
            }, that.steparr[i].time);
          });
        })();
      }
    },
    async nextstep() {
      let that = this;
      for (let i = 0; i < this.steparr2.length; i++) {
        await (function () {
          return new Promise(function (res, rej) {
            setTimeout(function () {
              that.steparr2[i].check = true;
              if (that.steparr2[i].id == 8 && that.steparr2[i].check == true) {
                console.log("id等于8，修改值", that.steparr2[i].check);
                that.$store.commit("ifonconduct/getdailogopendata", false);
                that.step2data = false;
                that.step3 = false;
                that.step4 = true;
              }
              res();
            }, that.steparr[i].time);
          });
        })();
      }
    },
    closestep4() {
      this.step2data = false;
      this.step3 = false;
      this.step4 = false;
      this.step5 = false;
      this.$emit("setcleardata", false);
      this.$store.commit("ifonconduct/getdiskclaerstatus", {});
    },
    // 上一步
    laststep() {
      this.step2data = false;
      this.$emit("setcleardata", false);
      this.$store.commit("ifonconduct/getdailogopendata", true);
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep {
  .el-dialog__header {
    padding-top: 16px;
    padding-left: 24px;
    padding-bottom: 16px;
  }
  .el-dialog__body {
    // padding: 24px;
    padding: 0;
  }
}
.checkbox {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  margin-top: 10px;
  ::v-deep {
    .el-checkbox {
      margin-right: 110px;
      margin-bottom: 16px;
    }
  }
}
.clearlist {
  padding: 24px;
  padding-top: 0px;
  &-mid {
    padding-top: 16px;
    border-top: 1px solid #f2f3f7;
  }
  &-foot {
    padding-top: 16px;
    border-top: 1px solid #f2f3f7;
  }
}
.btnbox {
  border-top: 1px solid #f2f3f7;
  width: 100%;
  height: 50px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  div:nth-child(1) {
    ::v-deep {
      .el-button {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 74px;
        height: 32px;
      }
    }
    margin-left: 24px;
  }
  .btn {
    margin-right: 24px;
    ::v-deep {
      .el-button {
        height: 32px !important;
        line-height: 0;
      }
    }
  }
}
.step3 {
  padding: 16px;
  .imgbox {
    @-webkit-keyframes rotation {
      from {
        -webkit-transform: rotate(0deg);
      }
      to {
        -webkit-transform: rotate(360deg);
      }
    }
    .imgitem {
      transform: rotate(360deg);
      animation: rotation 3s linear infinite;
    }
  }
  .stepbox {
    height: 200px;
    overflow: auto;
    .title {
      font-family: "Alibaba PuHuiTi";
      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
      color: #9999a1;
      margin: 2px 0;
      margin-left: 15px;
    }
    padding: 16px;
    .one {
      .onebox {
        padding: 4px 0;
        display: flex;
        align-items: center;
        .nochenk {
          color: #cecece;
        }
      }
    }
    .two {
      padding: 4px 0;
      display: flex;
      align-items: center;
      .nochenk {
        color: #cecece;
      }
    }
  }
}
.step4 {
  span {
    font-family: "Alibaba PuHuiTi";
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    color: #9999a1;
  }
  &-main {
    display: flex;
    align-items: center;
    span {
      font-family: "Alibaba PuHuiTi";
      font-style: normal;
      font-weight: 500;
      font-size: 16px;
      color: #2c2c35;
    }
    img {
      margin-right: 20px; 
    }
  }
  &-foot {
    padding: 16px 12px;
    border-top: 1px solid #cecece;
    .el-button {
      width: 60px;
      height: 32px;
      padding: 0;
    }
    &-btn {
      padding: 0 5px !important;
      width: 88px !important;
    }
  }
  ::v-deep {
    .el-dialog__body {
      padding: 0;
      padding-top: 36px;
      padding-left: 32px;
      padding-bottom: 40px;
    }
    .el-dialog__header {
      padding: 0;
    }
    .el-dialog__footer {
      padding: 0;
    }
  }
}
</style>