<template>
  <div class="empty">
    <div>
      <img src="@/assets/images/empty.png">
    </div>
    <div class="content">{{ content }}</div>
  </div>
</template>

<script>
export default {
  name: 'Empty',
  props: {
    content: {
      type: String,
      default: '暂无数据'
    }
  }
};
</script>

<style lang="scss" scoped>
.empty{
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  img{
    width: 80px;
    height: 80px
  }
  .content{
    margin-top: 12px;
    color: #767684;
  }
}
</style>
