<template>
  <div>
    <tag-name
      v-for="(item, index) in tagList"
      :key="index"
      :item="item"
      @click.native="tagSelect(item)"
    />
  </div>
</template>

<script>
import TagName from "./TagName";
export default {
  name: "TagList",
  components: {
    TagName,
  },
  props: {
    tagList: {
      type: Array,
      default: () => [],
    },
  },
  methods: {
    tagSelect(item) {
      this.$emit("tagSelect", item);
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
