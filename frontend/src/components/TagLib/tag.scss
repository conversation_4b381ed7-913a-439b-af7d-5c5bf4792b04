::v-deep {
  .el-table__header-wrapper th {
    padding: 4px 0;
  }

  /* table */
  .el-table__header-wrapper th {
    padding: 4px 0;

    .cell {
      font-weight: normal;
    }
  }

  /* drawer */
  .drawer-detail {
    font-size: 14px;
    padding-bottom: 56px;

    .el-drawer__header {
      margin-bottom: 0px;
      padding: 0px;
      text-indent: 16px;
      height: 56px;
      border-bottom: 1px solid #f2f3f7;
      line-height: 56px;
      color: #2c2c35;
      font-weight: 600;
    }

    .drawer-detail__content {
      padding: 16px;

      .detail-label {
        width: 100%;
        height: 22px;
        line-height: 22px;
        color: #2c2c35;
        font-size: 14px;
        font-weight: 600;
        margin-top: 24px;
        margin-bottom: 8px;
      }
    }

    .drawer-detail__footer {
      width: 100%;
      height: 56px;
      line-height: 56px;
      border-top: 1px solid #f2f3f7;
      position: absolute;
      left: 0;
      bottom: 0;
      padding: 0 16px;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: flex-end;

      .el-button {
        padding: 0;
        height: 32px;
      }
    }
  }

  .el-dialog {
    border-radius: 8px;
  }

  /* dialog */
  .el-dialog__body {
    padding-top: 0;
  }

  /* tag */
  .el-tag {
    padding: 0 4px;

    &:hover {
      cursor: pointer;
    }
  }

  .el-tag--light.el-tag--danger {
    background: #FCE7E7;
    border-color: #FCE7E7;
    color: #A41818;
  }

  .el-tag--dark.el-tag--danger {
    background: #FF4848;
    border-color: #FF4848;
    color: #FFFFFF;
  }

  .el-tag--light.el-tag--warning {
    background: #F9EDDF;
    border-color: #F9EDDF;
    color: #B76F1E;
  }

  .el-tag--dark.el-tag--warning {
    background: #FFAA5A;
    border-color: #FFAA5A;
    color: #FFFFFF;
  }

  .el-tag--light.el-tag--success {
    background: #E0F5EE;
    border-color: #E0F5EE;
    color: #006157;
  }

  .el-tag--dark.el-tag--success {
    background: #2BE0B6;
    border-color: #2BE0B6;
    color: #FFFFFF;
  }

  .el-tag--light.el-tag--positive {
    background: #E7F0FE;
    border-color: #E7F0FE;
    color: #1B428D;
  }

  .el-tag--dark.el-tag--positive {
    background: #116EF9;
    border-color: #116EF9;
    color: #FFFFFF;
  }

  .el-tag--light.el-tag--info {
    background: #DEE0E7;
    border-color: #DEE0E7;
    color: #2C2C35;
  }

  .el-tag--dark.el-tag--info {
    background: #767684;
    border-color: #767684;
    color: #FFFFFF;
  }

  .flex {
    display: flex;
    align-items: center
  }

  /* form */
  .el-form {
    .el-form-item {
      margin-bottom: 4px;
    }
  }

}