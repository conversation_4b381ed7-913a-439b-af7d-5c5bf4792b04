<template>
  <div
    class="box"
    id="main"
    ref="main"
  >
    <div class="title">{{title}}</div>
    <div
      class="back"
      @click="BACK"
    >
      <img
        src="../../assets/graph/icon_16_enter.svg"
        alt=""
      >
    </div>
  </div>
</template>

<script>
import G6 from '@antv/g6'
import { get_data, get_next, get_edge_json, alarm_info } from '@/api/graph'

export default {
  // 入参：
  // initValue
  // initType：目标类型，必传
  // ininId: 告警详情跳转使用的参数
  props: ['initValue', 'initType', 'initId', 'initTitle'],
  data () {
    return {
      g6: '',
      width: '',
      height: '',
      data: '',
      title:'图关联分析'
    }
  },
  created () {
    if (this.initTitle) {
      this.title = this.initTitle
    }
  },
  mounted () {
    this.INIT()
    this.GET_DATA()
  },
  methods: {
    GET_DATA () {
      let data = {}
      if (this.initValue && this.initType) {
        data = {
          str: this.initValue,
          type: this.initType
        }
        get_data(data).then(res => {
          let data = {
            nodes: [],
            edges: []
          }
          if (res.err === 0) {
            for (let i = 0; i < res.data.vertex.length; i++) {

              data.nodes.push({
                id: res.data.vertex[i].id,
                label: res.data.vertex[i].label,
                raw_type: res.data.vertex[i].type
              })
            }
            for (let i = 0; i < res.data.edge.length; i++) {
              data.edges.push({
                source: res.data.edge[i].from,
                target: res.data.edge[i].to,
                label: res.data.edge[i].label
              })
            }
            this.data = data
            this.CHANGE_NODE_EDGE()
            this.g6.data(this.data)
            this.g6.render()
          }
        })
      } else if (this.initId) {
        data = {
          _id: this.initId
        }
        alarm_info(data).then(res => {
          let data = {
            nodes: [],
            edges: []
          }
          if (res.err === 0) {
            for (let i = 0; i < res.data.vertex.length; i++) {

              data.nodes.push({
                id: res.data.vertex[i].id,
                label: res.data.vertex[i].label,
                raw_type: res.data.vertex[i].type
              })
            }
            for (let i = 0; i < res.data.edge.length; i++) {
              data.edges.push({
                source: res.data.edge[i].from,
                target: res.data.edge[i].to,
                label: res.data.edge[i].label
              })
            }
            this.data = data
            this.CHANGE_NODE_EDGE()
            this.g6.data(this.data)
            this.g6.render()
          }
        })
      }

    },
    INIT () {
      this.width = this.$refs.main.scrollWidth
      this.height = this.$refs.main.scrollHeight
      this.g6 = new G6.Graph({
        container: 'main',
        width: this.width,
        height: this.height,
        // 是否开启画布自适应，开启后图自动适配画布大小
        fitView: true,
        fitCenter: true,
        fitViewPadding: 50,
        // 渲染模式，可选canvas与svg
        renderer: 'canvas',
        // 指定边是否连入节点的中心
        // linkCenter: true,
        // 编辑模式
        modes: {
          // 支持的 behavior
          default: ['drag-node',
            {
              type: 'zoom-canvas',
              enableOptimize: true,
            },
            {
              type: 'drag-canvas',
              enableOptimize: true,
            },
            //  {
            //   type: 'activate-relations'
            // }
          ],
          edit: ['drag-canvas', 'zoom-canvas', 'click-select'],
          edit2: ['drag-node']
        },
        // 全局布局
        layout: {
          // 布局类型
          type: 'gForce',
          unitRadius: 200, // 圈距离
          strictRadial: false,  // 是否严格圈
          preventOverlap: true, // 防止节点重叠
          // // 防碰撞必须设置nodeSize或size,否则不生效，由于节点的size设置了40，虽然节点不碰撞了，但是节点之间的距离很近，label几乎都挤在一起，所以又重新设置了大一点的nodeSize,这样效果会好很多
          nodeSize: 80,
          linkDistance: 150, // 指定边距离为150
          nodeStrength: 5000, // 节点作用力，正数代表节点之间的引力作用，负数代表节点之间的斥力作用
          // workerEnabled: true,      // 可选，开启 web-worker
          // gpuEnabled: true          // 可选，开启 GPU 并行计算，G6 4.0 支持
        },
        // 默认节点样式
        defaultNode: { // 节点样式修改
          type: 'circle', // 设置节点类型
          size: 4, // 节点大小
          style: {
            fill: '#FFFFFF',
            stroke: '#FFFFFF',
          },
          labelCfg: { // 修改节点label样式
            style: {
              fill: '#2C2C35', // 字体颜色
              fontSize: 14 // 字体大小
            },
            position: 'bottom',
            textAlign: 'center',
          },
          // 配置图标`
          icon: {
            show: true,
          },

          cursor: 'pointer',
        },
        // 默认边样式
        defaultEdge: {
          type: 'line',
          style: {
            endArrow: {
              path: G6.Arrow.triangle(5, 5),
              fill: '#9999A1',
              stroke: '#9999A1'
            },
            lineWidth: 1,
          },
          labelCfg: {
            refY: 10,
            position: 'middle',
            autoRotate: true
          }
        },

      })

    },
    // 自定义节点planB
    CHANGE_NODE_EDGE () {
      this.g6.node((node) => {

        return this.CHECK_NODE_ICON(node.raw_type, node)

      });
      this.g6.edge((edge) => {
        let temp = {
          type: 'line',
          style: {
            endArrow: {
              path: G6.Arrow.triangle(5, 5),
              fill: '#9999A1',
              stroke: '#9999A1'
            },
            lineWidth: 1,
          },
          labelCfg: {
            refY: 10,
            position: 'middle',
            autoRotate: true
          }
        }

        return {
          ...temp
        }


      })
    },
    // 判断元素类型并沿用何种图标
    CHECK_NODE_ICON (raw_type, node) {
      let temp = {
        id: node.id,
        type: 'circle',
        size: [40, 40],
        label: node.label || 'N/A',
        style: {
          fill: '#FFFFFF',
          stroke: '#FFFFFF',
        },
      }
      switch (raw_type) {
        // IP
        case 'IP':
          return {
            ...temp,
            icon: {
              show: true,
              img: require('../../assets/graph/IP.svg')
            }
          };
        // 域名
        case 'DOMAIN':
          return {
            ...temp,
            icon: {
              show: true,
              img: require('../../assets/graph/icon_web_green.svg')
            }
          }
        // MAC
        case 'MAC':
          return {
            ...temp,
            icon: {
              show: true,
              img: require('../../assets/graph/MAC.svg')
            }
          }
        // 应用服务
        case 'APPSERVICE':
          return {
            ...temp,
            icon: {
              show: true,
              img: require('../../assets/graph/icon_APP.svg')
            }
          }
        // 锚域名
        case 'FDOMAIN':

          return {
            ...temp,
            icon: {
              show: true,
              img: require('../../assets/graph/icon_anchor.svg')
            }
          }
        // 证书CERT
        case 'CERT':
          return {
            ...temp,
            icon: {
              show: true,
              img: require('../../assets/graph/icon_certificate_green.svg')
            }
          }
        // 企业
        case 'ORG':
          return {
            ...temp,
            icon: {
              show: true,
              img: require('../../assets/graph/icon_enterprise.svg')
            }
          }
        // 指纹
        case 'SSLFINGER':
          return {
            ...temp,
            icon: {
              show: true,
              img: require('../../assets/graph/icon_fingerprint_green.svg')
            }
          }
        // UA
        case 'UA':
          return {
            ...temp,
            icon: {
              show: true,
              img: require('../../assets/graph/UA.svg')
            }
          }
        // 硬件类型
        case 'DEVICE':
          return {
            ...temp,
            icon: {
              show: true,
              img: require('../../assets/graph/icon_hardware.svg')
            }
          }
        // 系统类型
        case 'OS':
          return {
            ...temp,
            icon: {
              show: true,
              img: require('../../assets/graph/icon_system.svg')
            }
          }
        // 应用类型
        case 'APPTYPE':
          return {
            ...temp,
            icon: {
              show: true,
              img: require('../../assets/graph/icon_Apptype.svg')
            }
          }
        // 文件夹
        case 'Folder':
          let imgBox = {
            type: 'image',
            size: [42, 32],
            label: node.label
          }

          if (node.label === "IP") {
            return {
              ...imgBox,
              img: require('../../assets/graph/ip_默认.svg')
            };
          } else if (node.label === 'MAC') {
            return {
              ...imgBox,
              img: require('../../assets/graph/mac_默认.svg')
            }
          } else if (node.label === 'UA') {
            return {
              ...imgBox,
              img: require('../../assets/graph/UA_默认.svg')
            }
          } else if (node.label === 'DOMAIN') {
            return {
              ...imgBox,
              img: require('../../assets/graph/域名_默认.svg')
            }
          } else if (node.label === 'APPSERVICE') {
            return {
              ...imgBox,
              img: require('../../assets/graph/应用服务_默认.svg')
            }
          }

          else {
            return {
              ...imgBox,
              img: require('../../assets/graph/ip_默认.svg')
            }
          }
        default:
          return {
            ...temp,
            icon: {
              show: true,

            }
          };
      }
    },
    BACK () {
      const routeData = this.$router.resolve({
        path: '/newgraph',
        query: {
          initValue: this.initValue,
          initType: this.initType
        }
      })
      window.open(routeData.href, '_blank');
    }
  }
}
</script>

<style lang="scss">
.box {
  width: 100%;
  height: 100%;
  background: #f7f8fa;
  border-radius: 8px;
  position: relative;
  .title {
    font-size: 14px;
    color: #2c2c35;
    position: absolute;
    font-weight: 600;
    top: 16px;
    left: 16px;
  }
  .back {
    position: absolute;
    width: 32px;
    height: 32px;
    right: 16px;
    top: 16px;
    background: #ffffff;
    box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
  }
}
</style>
