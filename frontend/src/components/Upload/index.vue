<template>
  <div class="upload">
    <div ref="drag" class="drag">
      <div class="drag-icon-box">
        <i class="iconfont icon-nav_Upload_line"></i>
      </div>
      <div class="drag-message">
        <label for="file" class="drag-message-label">
          <input
            v-if="uploadTitle.includes('文件')"
            id="file"
            ref="fileInput"
            :accept="accept"
            class="drag-message-input"
            type="file"
            multiple
            name="file"
            @change="handleFileChange"
          />
          <input
            v-else
            id="file"
            webkitdirectory
            class="drag-message-input"
            type="file"
            multiple
            name="file"
            @change="handleFileChange"
          />
          <slot>
            <span class="drag-message-title">将{{ uploadTitle }}拖动到此处，或</span>
            <span class="drag-message-manual">点击选择{{ uploadTitle }}</span>
          </slot>
        </label>
      </div>
    </div>
    <div class="drag-list">
      <div
        v-for="(item, index) in files.slice(0, 50)"
        :key="index"
        class="item"
      >
        <div class="info">
          <div class="name">
            <i class="icon el-icon-check"></i>
            <div class="text">{{ item.name || item.file_name || item }}</div>
          </div>
          <div class="handle">
            <el-button type="text" @click="handleDel(index)">删除</el-button>
          </div>
        </div>
      </div>
    </div>
    <Confirm v-model="loading" :message="message" />
  </div>
</template>

<script>
import { uploadFile } from "@/api/offline";
import Confirm from "@/views/offline/import-data/component/Confirm.vue";
export default {
  name: "Upload",
  components: {
    Confirm,
  },
  props: {
    uploadTitle: {
      type: String,
      default: "文件",
    },
    value: {
      type: Array,
      default: () => [],
    },
    // 是否需要解析
    isParse: {
      type: Boolean,
      default: false,
    },
    // 是否上传到服务器
    isServer: {
      type: Boolean,
      default: false,
    },
    // 是否读取文件
    isRead: {
      type: Boolean,
      default: false,
    },
    // 文件类型
    accept:{
      type:String,
      required:true
    },
    type:{
      type:String,
      default:''
    },
    size:{
      type:String,
      default:''
    }
  },
  data() {
    return {
      file: null,
      uploadProgress: 0,
      loading: false,
      message:'数据包上传中，请耐心等待...'
    };
  },
  computed: {
    files: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      },
    },
  },
  watch: {
    uploadTitle() {
      this.files = [];
    },
  },
  async mounted() {
    // 给容器绑定相关的拖拽事件
    this.bindEvents();
  },
  methods: {
    bindEvents() {
      const drag = this.$refs.drag;
      // 被拖动的对象进入目标容器
      drag.addEventListener("dragover", (e) => {
        e.preventDefault();
        drag.style.borderColor = "#116EF9";
      });
      // 被拖动的对象离开目标容器
      drag.addEventListener("dragleave", (e) => {
        e.preventDefault();
        drag.style.borderColor = "#DEE0E7";
      });
      // 被拖动的对象进入目标容器，释放鼠标键
      drag.addEventListener("drop", (e) => {
        drag.style.borderColor = "#DEE0E7";
        let items = e.dataTransfer.items; // FileList 对象
        for (let i = 0; i <= items.length - 1; i++) {
          let item = items[i];
          if (item.kind === "file") {
            let entry = item.webkitGetAsEntry();
            let file = item.getAsFile();
            if(this.type){
              if(file&&(file.type === this.type)){
                this.getFileFromEntryRecursively(entry);
              }else{
                this.$message.error('上传文件列表只支持txt文件');
              }
            }else{
              if(this.size){
                if(file.size>this.size){
                  this.$message.error('文件最大支持10G，上传文件大小超过上限，请重新上传');
                  return;
                }
              }
              this.getFileFromEntryRecursively(entry);
            }
          }
        }
        e.preventDefault();
      });
    },
    getFileFromEntryRecursively(entry) {
      // 文件
      if (entry.isFile) {
        entry.file(
          (file) => {
            if (this.isRead) {
              this.parseTxt(file);
              return;
            }
            if (this.isServer) {
              this.submitFile(file);
              return;
            }
            if (this.isParse) {
              this.handleParse(file);
            } else {
              this.uploadFile(file);
            }
          },
          (e) => {}
        );
        // 文件夹
      } else {
        let reader = entry.createReader();
        reader.readEntries(
          (entries) => {
            entries.forEach((entry) => this.getFileFromEntryRecursively(entry));
          },
          (e) => {
            console.log(e);
          }
        );
      }
    },
    async uploadFile(file) {
      this.files.push(file);
    },
    handleFileChange(e) {
      let files = e.target.files;
      if (this.isRead) {
        this.parseTxt(files[0]);
        e.target.value = "";
        return;
      }
      if (this.isServer) {
        if(this.size){
          if(files[0].size>this.size){
            this.$message.error('文件最大支持10G，上传文件大小超过上限，请重新上传');
            return;
          }
        }
        this.submitFile(files[0]);
        return;
      }
      if (this.isParse) {
        this.handleParse(files[0]);
      } else {
        for (let i = 0; i < files.length; i++) {
          let file = files[i];
          this.uploadFile(file);
        }
      }
      // 清空文件，为了触发change事件
      e.target.value = "";
    },
    // 解析txt文件
    parseTxt(selectedFile) {
      let reader = new FileReader(); // 读取TXT起重要作用的
      reader.readAsText(selectedFile);
      reader.onload = (oFREvent) => {
        // 读取完毕从中取值
        let pointsTxt = oFREvent.target.result;
        this.files = [...this.files, ...pointsTxt.split("\n")];
      };
    },
    // 上传
    handleDel(index) {
      this.files.splice(index, 1);
    },
    // 解析
    handleParse(file) {
      const reader = new FileReader();
      reader.readAsText(file, "UTF-8");
      reader.onload = (evt) => {
        const fileString = evt.target.result;
        const arr = fileString.trim().split(",");
        arr.forEach((item) => {
          this.uploadFile(item);
        });
      };
    },
    // 上传到服务器
    async submitFile(file) {
      try {
        this.loading = true;
        const form = new FormData();
        form.append("file", file);
        const res = await uploadFile(form);
        this.files.push(res.data);
        this.$refs.fileInput.value = "";
        this.loading = false;
        this.$message.success("上传服务器成功");
      } catch (error) {
        this.loading = false;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.drag {
  width: 100%;
  height: 110px;
  border: 1px dashed #dee0e7;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: #eceffc;
  .drag-icon-box {
    .icon-nav_Upload_line {
      font-size: 20px;
      color: #767684;
    }
  }
  .drag-message {
    width: 100%;
    text-align: center;
    .drag-message-title {
      font-size: 14px;
      color: #606266;
    }
    .drag-message-label {
      width: 120px;
      height: 50px;
      height: auto;
      position: relative;
      overflow: hidden;
      .drag-message-input {
        position: absolute;
        left: -100px;
        top: -100px;
        z-index: -1;
        display: none;
      }
      .drag-message-manual {
        font-size: 14px;
        color: #4b87ff;
        cursor: pointer;
      }
    }
  }
}
.drag-list {
  margin-top: 8px;
  // max-height: 250px;
  overflow-y: scroll;
  .item {
    .info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .name {
        display: flex;
        align-items: center;
        .text {
          white-space: nowrap;
          overflow: hidden;
          width: 360px;
          text-overflow: ellipsis;
        }
        .icon {
          font-size: 12px;
          margin-right: 4px;
          font-weight: bold;
        }
        .el-icon-check {
          color: #116ef9;
        }
        .el-icon-warning-outline {
          font-size: 14px;
          color: red;
        }
      }
    }
  }
}
</style>