<template>
  <div class="downdrawer">
    <el-drawer
      title="下载列表"
      :visible="DownDrawerdata"
      direction="rtl"
      :size="1000"
      custom-class="demo-drawer"
      @close="closeDownDrawer"
      @open="openDownDrawer"
    >
      <el-tabs v-model="activeDown" type="card" @tab-click="downClick">
        <el-tab-pane label="pcap下载" name="first">
          <el-table
            :data="pcaplistData"
            :header-cell-style="{ 'text-align': 'left' }"
            :cell-style="{ 'text-align': 'left' }"
            style="padding: 0 10px; padding-bottom: 40px"
          >
            >
            <el-table-column
              property="id"
              label="任务ID"
              width="80"
            ></el-table-column>
            <el-table-column
              property="query"
              label="条件"
              align="left"
              min-width="180"
            >
              <template slot-scope="scope">
                <div class="querybox">
                  <div>
                    <div
                      v-if="
                        !scope.row.show_quey ||
                          scope.row.show_quey == '' ||
                          scope.row.show_quey == undefined
                      "
                    >
                      无
                    </div>
                    <div v-else>
                      {{ FMT_DOWNNAME(scope.row) }}
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column property="type" label="类型">
              <template slot="header">
                类型

                <el-tooltip
                  class="item"
                  effect="light"
                  placement="top"
                  popper-class="sessionidTooltip"
                >
                  <div slot="content">
                    快速下载：下载会话前20个包
                    <br />全量下载：下载会话的全部pcap数据
                  </div>
                  <svg-icon icon-class="pacpmsg-icon" />
                </el-tooltip>
              </template>
              <template slot-scope="scope">
                <span v-if="scope.row.type == '1'">全量下载</span>
                <span v-if="scope.row.type == '0'">快速/部分</span>
              </template>
            </el-table-column>
            <el-table-column
              property="user_name"
              label="用户名"
            ></el-table-column>
            <el-table-column
              property="created_time"
              label="下载时间"
              width="100"
            ></el-table-column>
            <el-table-column
              property="delete_time"
              label="保存时间"
              align="center"
            >
              <template slot-scope="scope">
                <div>剩余{{ scope.row.delete_time }}小时</div>
              </template>
            </el-table-column>
            <el-table-column property="state" label="状态">
              <template slot-scope="scope">
                <span v-if="scope.row.type == '-1'">错误</span>
                <span v-if="scope.row.state == '0'">准备数据</span>
                <span v-if="scope.row.state == '1'">可下载</span>
                <span v-if="scope.row.state == '2'">重新下载</span>
                <span v-if="scope.row.state == '3'">未找到</span>
                <span v-if="scope.row.state == '4'">待删除</span>
                <span v-if="scope.row.state == '5'">已删除</span>
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template slot-scope="scope">
                <svg-icon
                  v-if="scope.row.state == '1'"
                  icon-class="pcapactive-icon"
                  style="margin-right: 16px; cursor: pointer"
                  @click="pacpdownfile(scope)"
                />

                <svg-icon
                  v-if="scope.row.state != '1'"
                  icon-class="pcap-icon"
                  style="margin-right: 16px; cursor: not-allowed"
                />
                <svg-icon
                  style="cursor: pointer"
                  icon-class="delete-icon"
                  @click="sessiondeletedata(scope)"
                />
              </template>
            </el-table-column>
          </el-table>
          <div class="foot">
            <el-pagination
              :current-page="currentPage"
              :page-sizes="[10, 20, 30, 50]"
              :page-size="page_size"
              layout="total, sizes, prev, pager, next, jumper"
              :total="currenttotal_real"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            >
            </el-pagination>
          </div>
        </el-tab-pane>
        <el-tab-pane label="日志导出" name="second">
          <el-table
            :data="loglistData"
            :header-cell-style="{ 'text-align': 'left' }"
            :cell-style="{ 'text-align': 'left' }"
            style="padding: 0 10px; padding-bottom: 40px"
          >
            >
            <el-table-column
              property="id"
              label="任务ID"
              width="80"
            ></el-table-column>
            <el-table-column
              property="query"
              label="条件"
              width="200"
              align="left"
            >
              <template slot-scope="scope">
                <div class="querybox">
                  <div>
                    <div
                      v-if="
                        !scope.row.showQuery ||
                          scope.row.showQuery == '' ||
                          scope.row.showQuery == undefined
                      "
                    >
                      无
                    </div>
                    <div v-else>
                      {{ FMT_DOWNNAME2(scope.row) }}
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column property="task_type" label="类型" width="200">
              <template slot-scope="scope">
                <span v-if="scope.row.task_type == 1">会话分析</span>
                <span v-if="scope.row.task_type == 2">会话聚合</span>
                <span v-if="scope.row.task_type == 3">{{
                  "元数据_" + scope.row.metadata_type
                }}</span>
                <span v-if="scope.row.task_type == 4">元数据_HTTP</span>
                <span v-if="scope.row.task_type == 5">元数据_DNS </span>
                <span v-if="scope.row.task_type == 6">IP列表 </span>
                <span v-if="scope.row.task_type == 7">域名列表 </span>
                <span v-if="scope.row.task_type == 8">证书列表 </span>
                <span v-if="scope.row.task_type == 9">指纹列表 </span>

                <!-- <el-tooltip
                  class="item"
                  effect="light"
                  placement="top"
                  popper-class="atooltip"
                >
                  <div slot="content">
                    会话列表
                    <br />客户端IP，服务端IP，应用协议名，目的IP端口，<br />服务器MAC，客户端IP所在国家，服务端MA<br />服务端IP所在国家
                  </div>
                  <svg-icon
                    icon-class="eye-icon"
                    v-if="scope.row.task_type == '聚合项'"
                  />
                </el-tooltip> -->
              </template>
            </el-table-column>
            <el-table-column
              property="user_name"
              label="用户名"
            ></el-table-column>
            <el-table-column property="create_time" label="下载时间">
              <template slot-scope="scope">
                {{ scope.row.create_time }}
              </template>
            </el-table-column>
            <el-table-column
              property="delete_time"
              label="保存时间"
              align="center"
            >
              <template slot-scope="scope">
                <div>剩余{{ scope.row.delete_time }}小时</div>
              </template>
            </el-table-column>
            <el-table-column property="type" label="状态">
              <template slot-scope="scope">
                <span v-if="scope.row.type == '1'">待执行</span>
                <span v-if="scope.row.type == '2'">准备数据</span>
                <span v-if="scope.row.type == '3'">可下载</span>
                <span v-if="scope.row.type == '4'">已删除</span>
                <span v-if="scope.row.type == '-1'">错误</span>
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template slot-scope="scope">
                <svg-icon
                  v-if="scope.row.type != '3'"
                  icon-class="pcap-icon"
                  style="margin-right: 16px; cursor: not-allowed"
                />
                <svg-icon
                  v-if="scope.row.type == '3'"
                  icon-class="pcapactive-icon"
                  style="margin-right: 16px; cursor: pointer"
                  @click="logdownfile(scope)"
                />
                <svg-icon
                  icon-class="delete-icon"
                  style="cursor: pointer"
                  @click="logdelete(scope)"
                />
              </template>
            </el-table-column>
          </el-table>
          <div class="foot">
            <el-pagination
              :current-page="logcurrentPage"
              :page-sizes="[10, 20, 30, 50]"
              :page-size="logpage_size"
              layout="total, sizes, prev, pager, next, jumper"
              :total="logcurrenttotal_real"
              @size-change="handleSizeChangelog"
              @current-change="handleCurrentChangelog"
            >
            </el-pagination>
          </div>
        </el-tab-pane>
      </el-tabs>
      <div class="tip">
        <!-- <i class="symbol">*</i>已经下载的数据会在当天24点之后删除 -->
      </div>
    </el-drawer>
  </div>
</template>

<script>
import {
  getlogderivelist,
  getpcapdownlist,
  logdelete,
  pcapdown,
  sessiondelete,
} from "@/api/Conversational/conversational";
import { dictBook } from "@/api/user";
import { parseTime } from "@/utils";
import { getToken } from "@/utils/auth";
import axios from "axios";
import FileSaver from "file-saver";
export default {
  data() {
    return {
      // 抽屉设置项
      dialog: false,
      loading: false,
      activeDown: "first",
      pcaplistData: [],
      //==========
      // pcap分页设置
      currentPage: 1,
      page_size: 20,
      total: 0,
      currenttotal_real: 0,
      // ================
      // 日志导出分页设置
      logcurrentPage: 1,
      logpage_size: 20,
      tologtotaltal: 0,
      logcurrenttotal_real: 0,
      // 日志数据
      loglistData: [],
      //===============
      esArr: {},
    };
  },
  computed: {
    DownDrawerdata() {
      return this.$store.state.conversational.downDrawer;
    },
  },
  watch: {
    pcapdownlist: {
      handler(val) {},
      deep: true,
    },
  },
  created() {
    this.dictBook();
  },
  methods: {
    async dictBook() {
      const res = await dictBook();
      this.esArr = res.data.download_search_field;
    },
    // 打开抽屉的回调
    openDownDrawer() {
      this.$store.commit("conversational/downDrawerData", true);
      let param = {
        limit: this.page_size,
        page: this.currentPage,
        user_name: "",
        user_id: 1,
      };
      let logparam = {
        // search_list: [1, 2, 3, 4, 5],
        page: this.logcurrentPage,
        limit: this.logpage_size,
        user_id: 1,
      };

      getpcapdownlist(param).then((res) => {
        let newarr = [];
        this.currenttotal_real = res.data.total;
        res.data.records.forEach((item) => {
          item.created_time = parseTime(item.created_time);
          newarr.push(item);
        });
        this.pcaplistData = newarr;
      });
      // 获取日志导出列表
      getlogderivelist(logparam).then((res) => {
        let newarr = [];
        this.logcurrenttotal_real = res.data.total;
        res.data.record.forEach((item) => {
          item.create_time = parseTime(item.create_time);
          newarr.push(item);
        });
        this.loglistData = newarr;
      });
    },
    // 获取pcap下载列表
    getpcapdownlist() {
      let param = {
        limit: this.page_size,
        page: this.currentPage,
        user_name: "",
        user_id: 1,
      };
      getpcapdownlist(param).then((res) => {
        let newarr = [];
        this.currenttotal_real = res.data.total;
        res.data.records.forEach((item) => {
          item.created_time = parseTime(item.created_time);
          newarr.push(item);
        });
        this.pcaplistData = newarr;
      });
    },
    // 获取日志导出列表
    getlogderivelist() {
      let logparam = {
        // search_list: [1, 2, 3, 4, 5],
        page: this.logcurrentPage,
        limit: this.logpage_size,
        user_id: 1,
      };
      // 获取日志导出列表
      getlogderivelist(logparam).then((res) => {
        let newarr = [];
        this.logcurrenttotal_real = res.data.total;
        res.data.record.forEach((item) => {
          item.create_time = parseTime(item.create_time);
          newarr.push(item);
        });
        this.loglistData = newarr;
      });
    },
    // pcap下载分页
    handleSizeChange(val) {
      this.limit100 = [];
      this.pageRange = [1, 10];
      this.page_size = val;
      this.getpcapdownlist();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getpcapdownlist();
    },
    // =============
    // 日志导出分页
    handleSizeChangelog(val) {
      this.limit100 = [];
      this.pageRange = [1, 10];
      this.logpage_size = val;
      this.getlogderivelist();
    },
    handleCurrentChangelog(val) {
      this.logcurrentPage = val;
      this.getlogderivelist();
    },
    // ==============
    pacpdownfile(data) {
      data.row.activedata = !data.row.activedata;
      let param = {
        id: data.row.id + "",
      };
      if (data.row.state == 1) {
        pcapdown(param).then((res) => {
          if (res.err === 40005) {
            this.$message.error(res.msg);
          } else {
            const blob = res.data;
            let baseURL;
            if (process.env.VUE_APP_BASE_API === "") {
              baseURL = `${process.env.VUE_APP_BASE_API}`;
            } else {
              baseURL = process.env.VUE_APP_BASE_API;
            }
            let downUrl = baseURL + "/download/" + res.data;
            // let downUrl = "https://192.168.101.183" + "/download/" + res.data;

            // console.log(downUrl);
            // FileSaver.saveAs(downUrl);
            window.open(downUrl);
          }
        });
      } else {
        if (data.row.state == 0) {
          this.$message({
            type: "error",
            message: "准备数据中",
            duration: 800,
          });
        } else if (data.row.state == 2) {
          this.$message({
            type: "error",
            message: "请重新下载",
            duration: 800,
          });
        } else if (data.row.state == 3) {
          this.$message({
            type: "error",
            message: "数据已删除",
            duration: 800,
          });
        }
      }
    },
    logdownfile(data) {
      let param = {
        id: data.row.id,
      };
      let baseURL;
      if (process.env.VUE_APP_BASE_API === "") {
        baseURL = `${process.env.VUE_APP_BASE_API}/api`;
      } else {
        baseURL = process.env.VUE_APP_BASE_API;
      }
      axios({
        method: "POST",
        url: baseURL + "/task/register/download", // 后端下载接口地址
        responseType: "blob", // 设置接受的流格式
        headers: {
          token: `${getToken()}`,
        },
        data: {
          id: data.row.id,
        },
      }).then((res) => {
        if (res.err === 40005) {
          this.$message.error(res.msg);
        } else {
          const blob = new Blob([res.data], { type: "application/json" });
          FileSaver.saveAs(blob, res.headers["content-disposition"]);
        }
      });
    },
    downClick(tab, event) {},
    closeDownDrawer() {
      this.$store.commit("conversational/downDrawerData", false);
    },
    // pcap列表删除
    sessiondeletedata(data) {
      let newarr = [];
      sessiondelete({ id: data.row.id }).then((res) => {
        if (res.err == 0) {
          this.pcaplistData.forEach((item) => {
            if (data.row.id !== item.id) {
              newarr.push(item);
            }
          });
        }
        this.pcaplistData = newarr;
      });
    },
    // 日志导出删除
    logdelete(data) {
      let newarr = [];
      logdelete({ id: data.row.id }).then((res) => {
        if (res.err == 0) {
          this.loglistData.forEach((item) => {
            if (data.row.id !== item.id) {
              newarr.push(item);
            }
          });
        }
        this.loglistData = newarr;
      });
    },
    FMT_DOWNNAME(row) {
      let temp = [];
      let map = JSON.parse(row.show_quey);
      if (row.show_quey == '{"not":{},"and":{}}') return "无";
      for (let i in map.and) {
        for (let j of map.and[i]) {
          if (this.esArr.conn[i]) {
            temp.push(`${this.esArr.conn[i]?.Name || "未知"}：${j}`);
          } else if (this.esArr.dns[i]) {
            temp.push(`${this.esArr.dns[i]?.Name || "未知"}：${j}`);
          } else if (this.esArr.http[i]) {
            temp.push(`${this.esArr.http[i]?.Name || "未知"}：${j}`);
          } else if (this.esArr.ssl[i]) {
            temp.push(`${this.esArr.ssl[i]?.Name || "未知"}：${j}`);
          } else if (this.esArr.domain[i]) {
            temp.push(`${this.esArr.domain[i]?.Name || "未知"}：${j}`);
          } else if (i == "Finger") {
            temp.push(`指纹：${j}`);
          } else {
            temp.push(`未知：${j}`);
          }
        }
      }
      for (let i in map.not) {
        for (let j of map.not[i]) {
          if (this.esArr.conn[i]) {
            temp.push(`${this.esArr.conn[i]?.Name || "未知"}：${j}`);
          } else if (this.esArr.dns[i]) {
            temp.push(`${this.esArr.dns[i]?.Name || "未知"}：${j}`);
          } else if (this.esArr.http[i]) {
            temp.push(`${this.esArr.http[i]?.Name || "未知"}：${j}`);
          } else if (this.esArr.ssl[i]) {
            temp.push(`${this.esArr.ssl[i]?.Name || "未知"}：${j}`);
          } else if (this.esArr.domain[i]) {
            temp.push(`${this.esArr.domain[i]?.Name || "未知"}：${j}`);
          } else if (i == "Finger") {
            temp.push(`指纹：${j}`);
          } else {
            temp.push(`未知：${j}`);
          }
        }
      }
      let result = temp.toString("，");
      var stringWithoutDuplicates = result.replace(
        /会话ID：/g,
        function (match, i, str) {
          if (i !== 0) {
            return ""; // 替换掉除了第一个匹配项之外的所有“会话ID：”
          }
          return match; // 保留第一个“会话ID：”
        }
      );
      return stringWithoutDuplicates;
    },
    FMT_DOWNNAME2(row) {
      let temp = [];
      let map = JSON.parse(row.showQuery);
      if (row.showQuery == '{"not":{},"and":{}}') return "无";
      for (let i in map.and) {
        for (let j of map.and[i]) {
          if (this.esArr.conn[i]) {
            temp.push(`${this.esArr.conn[i]?.Name || "未知"}：${j}`);
          } else if (this.esArr.dns[i]) {
            temp.push(`${this.esArr.dns[i]?.Name || "未知"}：${j}`);
          } else if (this.esArr.http[i]) {
            temp.push(`${this.esArr.http[i]?.Name || "未知"}：${j}`);
          } else if (this.esArr.ssl[i]) {
            temp.push(`${this.esArr.ssl[i]?.Name || "未知"}：${j}`);
          } else if (this.esArr.domain[i]) {
            temp.push(`${this.esArr.domain[i]?.Name || "未知"}：${j}`);
          }
        }
      }
      for (let i in map.not) {
        for (let j of map.not[i]) {
          if (this.esArr.conn[i]) {
            temp.push(`${this.esArr.conn[i]?.Name || "未知"}：${j}`);
          } else if (this.esArr.dns[i]) {
            temp.push(`${this.esArr.dns[i]?.Name || "未知"}：${j}`);
          } else if (this.esArr.http[i]) {
            temp.push(`${this.esArr.http[i]?.Name || "未知"}：${j}`);
          } else if (this.esArr.ssl[i]) {
            temp.push(`${this.esArr.ssl[i]?.Name || "未知"}：${j}`);
          } else if (this.esArr.domain[i]) {
            temp.push(`${this.esArr.domain[i]?.Name || "未知"}：${j}`);
          }
        }
      }
      return temp.toString("，");
    },
  },
};
</script>

<style lang="scss" scoped>
.downdrawer {
  .querybox {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }

  ::v-deep .el-drawer.rtl {
    overflow: scroll;
  }
  .foot {
    width: 1000px;
    height: 55px;
    background: #ffffff;
    box-shadow: 0px 6px 18px rgba(45, 47, 51, 0.14);
    border-radius: 2px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    position: fixed;
    bottom: 0;
    right: 0;
  }
  ::v-deep {
    .el-tabs--card > .el-tabs__header {
      margin-left: 10px;
      border: 0;
    }

    .el-tabs--card > .el-tabs__header .el-tabs__item {
      width: 72px;
      height: 26px;
      padding: 0;
      border: 0;
      border-radius: 4px;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #000000;
    }

    .el-tabs--card > .el-tabs__header .el-tabs__item.is-active {
      width: 72px;
      height: 26px;
      padding: 0;
      background: #ffffff;
      border-radius: 4px;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #116ef9;
      // margin: 0 10px !important;
    }
  }

  ::v-deep .el-tabs__nav {
    width: 169px;
    height: 34px !important;
    border: 0;
    background: #f2f3f7;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: space-around;
    transform: translateX(0px) !important;
  }
  ::v-deep .el-table__row > td {
    border: none;
  }
  ::v-deep .el-table::before {
    height: 0px;
  }
  .downdrawerbox {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  ::v-deep {
    .el-drawer {
      .el-drawer__header {
        padding: 16px;
      }
      .el-drawer__body {
        padding: 16px 6px;
        position: relative;
        .tip {
          position: absolute;
          right: 16px;
          top: 24px;
          color: #767684;
          .symbol {
            color: red;
            padding-right: 2px;
          }
        }
      }
    }
  }
}
</style>
