<template>
  <el-dialog
    :visible.sync="isShow"
    width="960px"
    append-to-body
    custom-class="tag"
    :destroy-on-close="true"
    @close="close"
  >
    <div slot="title" class="title">
      <div>标签库</div>
      <Tab v-if="isShowAside" v-model="activeIndex" :tab-data="tabData" />
    </div>
    <div class="list">
      <div class="content">
        <component
          :is="componentId"
          :ref="componentId"
          :is-del="isDel"
          :tag_target_type="tag_target_type"
          :is-show-aside="isShowAside"
          :select-tags="selectTags"
          @tagSelect="tagSelect"
        ></component>
      </div>
      <div class="aside">
        <div class="aside-content">
          <SelectTag :select-tags="selectTags" @clear="clear" />
        </div>
      </div>
    </div>
    <footer>
      <div>
        <el-button
          size="small"
          icon="el-icon-plus"
          plain
          @click="tagAddVisible = true"
        >
          添加新标签
        </el-button>
      </div>
      <div>
        <el-button size="small" plain @click="isShow = false"> 关闭 </el-button>
        <el-button size="small" plain @click="handleReset"> 重置 </el-button>
        <el-button size="small" type="primary" @click="handleConfirm">
          确定
        </el-button>
      </div>
    </footer>
    <TagAdd v-model="tagAddVisible" :libary-type="libaryType" @getList="init" />
  </el-dialog>
</template>
  
<script>
import Mixins from "@/mixins";
import Tab from "./component/Tab.vue";
import Recommend from "./recommend.vue";
import All from "./all.vue";
import TagAdd from "./component/TagAdd.vue";
import SelectTag from "./component/SelectTag.vue";
import { uniqBy, cloneDeep } from "lodash";
export default {
  name: "TagView",
  components: {
    Tab,
    Recommend,
    All,
    SelectTag,
    TagAdd,
  },
  mixins: [Mixins],
  props: {
    tagLabels: {
      type: Array,
      default: () => [],
    },
    /**
       * @title 标签库类型
       * @param
       * 1:证书标签库
       * 2:会话分析标签库
       */
    //
    libaryType: {
      type: Number,
      default: 1,
    },
    /**
       * 是否展示测边栏
       */
    isShowAside: {
      type: Boolean,
      default: true,
    },
    /**
       * 标签类型
       */
    tag_target_type: {
      type: Number,
    },
    /**
       * 标签是否可删除标识
       */
    isDel: {
      type: Boolean,
      default: true,
    },
    isAdd:{
      type:Boolean,
      default:false
    }
  },
  data() {
    return {
      tabData: [
        { label: "全部", key: "All" },
        { label: "推荐", key: "Recommend" },
      ],
      activeIndex: 0,
      selectTags: [],
      tagAddVisible: false,
      cloneSelectTags: [],
    };
  },
  computed: {
    componentId() {
      return this.tabData[this.activeIndex].key;
    },
    // 是否为会话分析标签库
    isLibaryType() {
      return this.libaryType === 2;
    },
  },
  watch: {
    isShow(val) {
      if (val) {
        if(this.isAdd){
          this.selectTags=[];
          this.init();
          return;
        }
        if (this.tagLabels.length) {
          // 选中标签
          this.selectTags = [...this.tagLabels];
          this.cloneSelectTags = cloneDeep(this.selectTags);
          this.init();
        }
      }
    },
  },
  methods: {
    init() {
        this.$refs?.[this.componentId]?.handleSearch();
    },
    tagSelect(val) {
      val.forEach((item) => {
        // 1. 该变选择状态
        item.effect = item.effect === "light" ? "dark" : "light";
        // 2. 选择标签的数据放到一起，并去重
        if (item.effect === "dark") {
          this.selectTags.push(item);
          this.selectTags = uniqBy(this.selectTags, "tag_id");
        } else if (item.effect === "light") {
          this.selectTags = this.selectTags.filter(
            (tag) => tag.tag_id !== item.tag_id
          );
        }
      });
    },
    close() {
      this.activeIndex = 0;
      this.selectTags = [];
      if (this.$refs.All) {
        this.$refs.All.activeIndex = 0;
        this.$refs.All.elTreeData=[];
      }
    },
    clear(item) {
      this.selectTags = this.selectTags.filter(
        (tag) => tag.tag_id !== item.tag_id
      );
      item.effect = "light";
      this.init();
    },
    // 确定标签
    handleConfirm() {
      let tagsId = this.selectTags.map((item) => item.tag_id);
      if (tagsId.length > 20) {
        this.$message.error("已选标签数量不能超过20个");
        return;
      }
      this.$emit(
        "modifyLabels",
        tagsId,
        this.selectTags,
        (val) => val && (this.isShow = false)
      );
    },
    // 重置
    handleReset() {
      this.selectTags = this.cloneSelectTags;
        this.$refs[this.componentId]?.reset();
        this.init();
    },
  },
};
</script>
  
  <style lang="scss" scoped>
  ::v-deep {
    .el-dialog__body {
      padding: 0;
    }
  }
  .title {
    display: flex;
    align-items: center;
    font-size: 14px;
    .tab {
      margin-left: 40%;
    }
  }
  .list {
    height: 586px;
    display: flex;
    border-top: 1px solid #f2f3f7;
    display: flex;
    .aside,
    .content {
      height: 100%;
      overflow: auto;
    }
    .content {
      flex: 1;
      // padding: 12px;
      box-sizing: border-box;
    }
    .aside {
      width: 200px;
      &-content {
        background: #f7f8fa;
        width: 100%;
        min-height: 100%;
      }
    }
  }
  footer {
    border-top: 1px solid #f2f3f7;
    background: #fff;
    padding: 12px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    height: 56px;
    justify-content: space-between;
  }
  </style>