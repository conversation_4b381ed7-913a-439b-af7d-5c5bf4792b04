import request from "@/utils/request";
// 标签库查询（全量展示）
export function getTagSearch(data) {
  return request({
    url: "/tag/search",
    method: "POST",
    data,
  });
}

// 标签库查询（全量展示）
export function getTagAdd(data) {
  return request({
    url: "/tag/add",
    method: "POST",
    data,
  });
}

// 查询标签分类
export function getListTagAttribute() {
  return request({
    url: "/tag/listTagAttribute ",
    method: "GET"
  });
}

// 标签推荐
export function recommendTag(data) {
  return request({
    url: "/tag/session/recommend",
    method: "GET",
  });
}
