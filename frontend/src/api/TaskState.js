import request from '@/utils/request'

// IP关系图数据获取
export function communication_list(data) {
  return request({
    url: '/workbench/communication/list',
    method: 'post',
    data
  })
}

// 内网IP网段列表数据获取
export function recent_flow(data) {
  return request({
    url: '/workbench/recent/flow',
    method: 'get',
    params: data
  })
}

// 矿池IP列表
export function get_pool_list(data) {
  return request({
    url: '/workbench/pool/list',
    method: 'get',
    params: data
  })
}
