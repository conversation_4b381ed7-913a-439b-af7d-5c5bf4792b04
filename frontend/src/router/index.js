import Vue from "vue";
import Router from "vue-router";

Vue.use(Router);

/* Layout */
import Layout from "@/layout";

export const constantRoutes = [
  {
    path: "/login",
    name: "Login",
    component: () => import("@/views/login/index"),
    hidden: true,
  },
  {
    path: "/404",
    component: () => import("@/views/404"),
    hidden: true,
  },

  {
    path: "/",
    redirect: "/",
    component: Layout,
    children: [
      {
        path: "",
        name: "workbench",
        component: () => import("@/views/WorkBench/index"),
        meta: {
          title: "实时分析",
          icon: "icon-workbench",
          iconClass: ["icon-nav_Vulnerability_line", "icon-nav_Vulnerability_fill"],
          key: "/",
        },
      },
    ],
  },
  {
    path: "/offline",
    component: Layout,
    children: [
      {
        path: "",
        name: "Offline",
        component: () => import("@/views/offline/index"),
        meta: {
          title: "离线分析",
          icon: "icon-session",
          iconClass: ["icon-nav_OfflineAnalysis_line", "icon-nav_OfflineAnalysis_fill"],
          key: "/Offline",
        },
      },
    ],
  },
  {
    path: "/SessionAnalyse",
    component: Layout,
    children: [
      {
        path: "",
        name: "SessionAnalyse",
        component: () => import("@/views/SessionAnalyse/index"),
        meta: {
          title: "会话分析",
          icon: "icon-session",
          // keepAlive: true,
          iconClass: ["icon-nav_Analysis_line", "icon-nav_Analysis_fill"],
          key: "/SessionAnalyse",
        },
      },
    ],
  },
  {
    path: "/modelmanage",
    component: Layout,
    hidden: process.env.VUE_APP_TITLE === 'traffic' ? false : true,
    children: [
      {
        path: "modelmanage",
        name: "modelmanage",
        component: () => import("@/views/modelmanage/index"),
        meta: {
          title: "模型管理",
          icon: "navicon_Model",
          iconClass: ["icon-nav_Model_line", "icon-nav_Model_fill"],
          key: "/modelmanage/modelmanage",
        },
      },
    ],
  },
  {
    path: "/caution",
    component: Layout,
    children: [
      {
        path: "caution",
        name: "caution",
        component: () => import("@/views/caution/index"),
        meta: {
          title: "告警信息",
          icon: "icon-waring",
          iconClass: ["icon-nav_Alarm_line", "icon-nav_Alarm_fill"],
          key: "/caution/caution",
        },
      },
    ],
  },
  {
    path: "/newgraph",
    hidden: true,
    component: () => import("@/views/newgraph/index.vue"),
    meta: {
      title: "智能图谱",
      icon: "link",
    },
  },
  {
    path: "/search",
    hidden: true,
    component: () => import("@/views/newgraph/search/index.vue"),
    meta: {
      title: "search",
      icon: "tts-link",
      iconClass: ["icon-nav_Task_line", "icon-nav_Task_fill"],
      key: "/search",
    },
  },
  {
    path: "/graph",
    hidden: true,
    component: () => import("@/views/graph/index.vue"),
    meta: {
      title: "graph",
      icon: "link",
    },
  },
  {
    path: "/systeminfo",
    component: Layout,
    children: [
      {
        path: "",
        name: "",
        component: () => import("@/views//SystemInfo/index"),
        meta: {
          title: "系统信息",
          icon: "icon-systemifon",
          iconClass: ["icon-nav_Setting_line", "icon-nav_Setting_fill"],
          key: "/systeminfo",
        },
      },
    ],
  },
  // {
  //   path: "/tag",
  //   component: Layout,
  //   children: [
  //     {
  //       path: "",
  //       name: "",
  //       component: () => import("@/views/test/tag"),
  //       meta: {
  //         title: "标签库",
  //         iconClass: ["icon-nav_Setting_line", "icon-nav_Setting_fill"],
  //         icon: "icon-systemifon",
  //       },
  //     },
  //   ],
  // },
  // 404 page must be placed at the end !!!
  {
    path: "*",
    redirect: "/404",
    hidden: true,
  },
];

const createRouter = () =>
  new Router({
    mode: "history",
    scrollBehavior: () => ({
      y: 0,
    }),
    routes: constantRoutes,
  });

const router = createRouter();

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter();
  router.matcher = newRouter.matcher; // reset router
}

export default router;
