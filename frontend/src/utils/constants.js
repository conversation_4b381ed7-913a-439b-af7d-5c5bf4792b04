// 标签颜色
export const TAG_COLOR = {
  danger: 0,
  warning: 1,
  success: 2,
  positive: 3,
  info: 4,
};
// 证书详情table展示的字段
export const TAG_TABLE = {
  ASN1MD5: "ASN格式MD5",
  ASN1SHA1: "ASN格式SHA1",
  CN: "使用者通用名",
  CertID: "证书ID/指纹",
  Duration: "有效周期_秒",
  Extension: "扩展信息",
  "Extension.authorityInfoAccess": "- 授权信息访问",
  "Extension.authorityKeyIdentifier": "- 授权密钥标识符",
  "Extension.basicConstraints": "- 基本约束",
  "Extension.certificatePolicies": "- 证书策略",
  "Extension.crlDistributionPoints": "- CRL分发点",
  // "Extension.ct_precert_scts": "- SCT列表",
  "Extension.extendedKeyUsage": "- 增强型密钥用法",
  "Extension.keyUsage": "- 密钥用法",
  "Extension.subjectAltName": "- 使用者可选名称",
  "Extension.subjectKeyIdentifier": "- 使用者密钥标识符",
  Format: "格式",
  FatherCertID: "父证书ID",
  Issuer: "颁发者",
  "Issuer.C": "- 国家",
  "Issuer.CN": "- 通用名",
  "Issuer.O": "- 组织名",
  "Issuer.OU": "- 组织部门",
  "Issuer.L": "- 地址",
  "Issuer.ST": "- 省份",
  IssuerMD5: "颁发者哈希",
  // labels: "证书标签",
  NotAfter: "有效期终止",
  NotBefore: "有效期起始",
  PemMD5: "PEM格式MD5",
  PemSHA1: "PEM格式SHA1",
  PublicKey: "公钥",
  SAN: "扩展域名",
  // SelfSignedCertatureAlgorithm: "签名哈希算法",
  SerialNumber: "序列号",
  SignatureAlgorithm: "签名算法",
  Subject: "使用者",
  "Subject.C": "- 国家",
  "Subject.CN": "- 通用名",
  "Subject.L": "- 地址",
  "Subject.O": "- 组织名",
  "Subject.OU": "- 组织部门",
  "Subject.ST": "- 省份",
  SubjectMD5: "使用者哈希",
  Usage: "密钥用法",
  Version: "版本",
};
// 切换任务状态颜色
export const TASK_STATE = {
  1: "#FF9534",
  2: "#07D4AB",
  3: "#2C2C35",
};
// 导入进度当前批次状态
export const TASK_PROGRESS = {
  1: "等待导入",
  2: "正在导入",
  3: "导入完成"
};
// 导入进度当前批次状态对应颜色
export const TASK_PROGRESS_COLOR = {
  0: "#FF9534",
  1: "#2C2C35",
  2: "#07D4AB",
  3: "#F91111",
  "-1": "#F91111",
};
// ECharts 饼图颜色集合
export const E_CHART_COLOR=[ "#1B428D",
  "#116EF9",
  "#8ABCFF",
  "#D1E3FF",
  "#008775",
  "#07D4AB",
  "#7FFAD5",
  "#E0F5EE",
  "#8B11EB",
  "#CB11F9",
  "#E59AFF",
  "#ECCDFF",
  "#B76F1E",
  "#FF9534",
  "#FFCA99",
  "#F8DDBF",
  "#A41818",
  "#F91111",
  "#FF9292",
  "#FFCACA",
];
