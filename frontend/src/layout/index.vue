<template>
  <div class="layout-box">
    <header class="layout-header">
      <nav-bar />
    </header>
    <article class="layout-content">
      <aside class="layout-aside">
        <side-bar />
        <Shortcut />
      </aside>
      <main class="layout-main">
        <transition
          name="fade-transform"
          mode="out-in"
        >
          <router-view />
        </transition>
      </main>
    </article>
  </div>
</template>

<script>
import ResizeMixin from "./mixin/ResizeHandler";
import SideBar from "./components/sidebar";
import NavBar from "./components/navbar";
import Shortcut from './components/shortcut/index.vue';

// import Taghead from "./components/taghead";
export default {
  name: "Layout",
  components: {
    SideBar,
    NavBar,
    Shortcut,
    // Taghead,
  },
  mixins: [ResizeMixin],
  computed: {},
  methods: {},
};
</script>

<style lang="scss" scoped>
// @import '~@/styles/mixin.scss';
@import '~@/styles/variables.scss';
.layout-box {
  width: 100%;
  height: 100vh;
  // overflow: hidden;
  position: relative;
  background-color: #eef1f9;
  .layout-header {
    width: 100%;
    height: 46px;
    position: relative;
    background-color: #ffffff;
    // box-shadow: 0px 15px 19px rgba(0, 0, 0, 0.05);
    border-radius: 0px 0px 10px 10px;
    z-index: 701;
  }
  .layout-content {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    // padding: 0 10px;
    box-sizing: border-box;
    .layout-aside {
      width: 173px;
      height: 100%;
      position: relative;
      background: #ffffff;
      // box-shadow: 0px 6px 12px rgba(0, 0, 0, 0.08);
      // border-radius: 10px;
      // overflow-y: hidden;
      padding-top: 26px;
      padding-bottom: $sidebarPaddingBottom;
      box-sizing: border-box;
    }
    .layout-main {
      width: 100%;
      height: 100%;
      position: relative;
      overflow-y: auto;
      overflow-x: hidden;
      background-color: transparent;
      height: calc(100% - 40px);
      padding: 16px;
      padding-bottom: 0;
      // border-radius: 10px;
    }
  }
  > aside {
    width: 100%;
    height: 10px;
    z-index: 700;
    background-color: inherit;
    position: relative;
  }
  .layout-taghead {
    width: 100%;
    height: 50px;
  }
}
</style>
