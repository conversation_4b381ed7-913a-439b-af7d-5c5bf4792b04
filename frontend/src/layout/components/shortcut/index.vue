
<template>
  <div class="shortcut">
    <div class="btn-box">
      <template v-if="!$isTraffic">
        <div v-if="diskmode == '换盘模式'" @click="Changeharddisk">
          <div class="change-harddisk">
            <svg-icon icon-class="change-harddisk2" />
          </div>
          <span>更换硬盘</span>
        </div>
        <div v-if="diskmode == '读盘模式'" @click="Readdisk">
          <div class="change-harddisk">
            <svg-icon icon-class="change-harddisk2" />
          </div>
          <span>读取硬盘</span>
        </div>
      </template>
      <div @click="DataProcessing">
        <div class="clear">
          <img src="@/assets/images/data_clean.png" />
        </div>
        <span class="clear-text">数据清理</span>
      </div>
    </div>
    <div class="time-box">
      <div>
        <div>启动时间</div>
        <div>{{ start_time }}</div>
      </div>
      <div>
        <div>系统运行时长</div>
        <div>{{ run_time }}</div>
      </div>
      <!-- <aside>
        <svg-icon icon-class="timeimage" />
      </aside> -->
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs';
import { getHDDinfo, getdiskstatus } from '@/api/SystemData/systemdata';
export default {
  name: 'Shortcut',
  data () {
    return {
      showdailog: false,
      start_time: '',
      run_time: ''
    };
  },
  computed: {
    diskmode () {
      return this.$store.state.ifonconduct.diskmode;
    }
  },
  watch: {
    '$store.state.long.tableType20': {
      handler: function (val, old) {
        this.start_time = dayjs(
          val.forensics[0].system_start_time * 1000
        ).format('YYYY-MM-DD HH:mm:ss');
        // let date1 = dayjs()
        // let date2 = dayjs(val.forensics[0].system_start_time * 1000)
        // date1.diff(date2)
        // this.run_time = dayjs(val.forensics[0].system_start_time * 1000).fromNow(true, 'H')
        this.run_time = this.handlerDateDurationCurrent(this.start_time);
      }
    }
  },
  methods: {
    // 更换硬盘
    Changeharddisk () {
      getdiskstatus().then((res) => {
        if (res.err == 0) {
          this.$store.commit('ifonconduct/getdiskstatus', res.data);
          // 模拟获取到磁盘信息
          getHDDinfo().then((res) => {
            if (res.err == 0) { this.$store.commit('ifonconduct/getdrivedata', res.data); }
          });
          this.$store.commit('ifonconduct/Replacingdrive', true);
        }
      });
    },
    // 读取磁盘
    Readdisk () {
      getdiskstatus().then((res) => {
        if (res.err == 0) {
          this.$store.commit('ifonconduct/getdiskstatus', res.data);
          // 模拟获取到磁盘信息
          getHDDinfo().then((res) => {
            if (res.err == 0) { this.$store.commit('ifonconduct/getdrivedata', res.data); }
          });
          this.$store.commit('ifonconduct/Readdiskdata', true);
        }
      });
    },
    // 数据清理
    DataProcessing () {
      this.showdailog = !this.showdailog;
      this.$store.commit('ifonconduct/getdailogopendata', true);
    },
    handlerDateDurationCurrent (time) {
      const d1 = new Date(time);
      const d2 = new Date();

      const cha = Math.abs(d2.getTime() - d1.getTime());
      const days = parseInt(cha / (24 * 60 * 60 * 1000));
      const hours = parseInt((cha % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000));
      const mins = parseInt((cha % (60 * 60 * 1000)) / (60 * 1000));
      if (days) {
        return `${days}天 ${hours}时 ${mins}分`;
      } else if (hours) {
        return `${hours}天 ${mins}时`;
      } else {
        return `${mins}分`;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
@import "~@/styles/variables.scss";

.shortcut {
  width: 100%;
  height: $sidebarPaddingBottom;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  // align-items: center;
  background-color: #fff;
  bottom: 46px;
  position: absolute;

  .title {
    width: 112px;
    height: 13px;
    font-size: 10px;
    color: #949494;
    display: flex;
    justify-content: space-between;
    align-items: center;

    >span:nth-of-type(1),
    >span:nth-of-type(3) {
      width: 30px;
      height: 1px;
      background-color: #f2f3f7;
    }
  }

  .btn-box {
    width: 112px;
    height: calc(100% - 13px - 127px);
    display: flex;
    flex-direction: column;
    margin-left: 12px;
    justify-content: space-evenly;

    // align-items: center;
    >div {
      padding-left: 12px;
      width: 120px;
      height: 46px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      position: relative;
      font-size: 12px;
      cursor: pointer;

      span {
        font-family: "Alibaba PuHuiTi";
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        color: #2c2c35;
      }
    }

    >div:hover {
      background: #f7f8fa;

      .cleanstyle {
        fill: currentColor;
        color: #df0c0c;
        font-size: 16px;
      }
    }

    .clear {
      width: 22px;
      height: 22px;
      border-radius: 6px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 16px;
      margin-right: 4px;

      .cleanstyle {
        fill: currentColor;
        color: #9999a1;
        font-size: 16px;
      }
    }

    .change-harddisk {
      width: 22px;
      height: 22px;
      border-radius: 6px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 16px;
      margin-right: 4px;
      // position: absolute;
      // left: 0;
    }
  }

  .time-box {
    width: 100%;
    height: 127px;
    // background: #116ef9;
    box-shadow: 0px 6px 12px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    color: #2c2c35;
    font-size: 12px;
    box-sizing: border-box;
    padding: 16px 24px;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    >div {
      >div:nth-of-type(1) {
        color: #9999a1;
        margin-bottom: 5px;
      }
    }
  }
}
</style>
