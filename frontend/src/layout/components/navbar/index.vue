<template>
  <div class="navbar">
    <!-- <div
      class="left"
      @click="
        () => {
          $router.push('/');
        }
      "
    >
      <img src="../../../assets/images/20230331-162752.png" alt="" style="width:80%" />
    </div> -->
    <div class="left">{{ $title }}</div>
    <div class="right">
      <!-- <el-dropdown style="margin-right: 15px" @command="HANDLE_COMMAND">
        <el-button type="primary" size="mini">
          系统<i class="el-icon-arrow-down el-icon--right"></i>
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="a">关闭主机</el-dropdown-item>
          <el-dropdown-item command="b">重启主机</el-dropdown-item>
          <el-dropdown-item command="c">切换账号</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown> -->
      <div class="msg">
        <el-badge :is-dot="downdot" class="downicon">
          <svg-icon icon-class="速度_speed 1" class="down" @click="TASK_DOWN" />
        </el-badge>
      </div>
      <div class="msg">
        <el-badge :is-dot="downdot" class="downicon">
          <el-button
            type="text"
            class="download-button"
            @click="opendownDrawer"
          >
            <svg-icon icon-class="icon-down" class="icon-style" />
            <span class="downloadText">下载中心</span>
          </el-button>
        </el-badge>
        <!-- <el-badge
          :is-dot="msgdot"
          class="msgicon"
        >
          <img
            src="../../../assets/images/msgicon.svg"
            alt=""
          />
        </el-badge> -->
      </div>

      <el-dropdown style="margin-right: 15px" @command="HANDLE_COMMAND">
        <div class="Userbox">
          <img src="../../../assets/images/blue-user.svg" alt="" />
          <div class="Username">
            <!-- <el-button type="primary" size="mini">
              系统<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button> -->
            <div class="adminbox">
              <span style="font-weight: 700; font-size: 10px">{{ $store.state.user.show_username }}</span>
              <span>{{ $store.state.user.name }}</span>
            </div>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="a">关闭主机</el-dropdown-item>
              <el-dropdown-item command="b">重启主机</el-dropdown-item>
              <el-dropdown-item command="c">修改密码</el-dropdown-item>
              <!-- <el-dropdown-item command="e">关于系统</el-dropdown-item> -->
              <el-dropdown-item command="d">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </div>
        </div>
      </el-dropdown>
    </div>
    <el-dialog
      :visible.sync="passwordDialog"
      width="30%"
      :append-to-body="true"
      class="system-diglog"
    >
      <div slot="title">
        <div>
          <i class="iconfont icon-a-16_alert"></i>
          {{ title }}
        </div>
      </div>
      <div>
        <div class="tip">该操作将使服务器{{ confirmText }}，请谨慎操作。</div>
        <el-input
          v-model="password"
          placeholder="请输入服务器密码"
          show-password
          autofocus
        ></el-input>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="passwordDialog = false">取消</el-button>
        <el-button
          type="danger"
          size="small"
          :disabled="!password"
          @click="SYSTEM_SUBMIT"
        >确认{{ confirmText }}</el-button>
      </span>
    </el-dialog>
    <!-- 修改密码 -->
    <el-dialog
      :visible.sync="resetPasswordDialog"
      width="30%"
      :append-to-body="true"
      class="system-diglog"
    >
      <div slot="title">
        <div>{{ title }}</div>
        <div></div>
        <div>
          <!-- <i class="el-icon-close"></i> -->
        </div>
      </div>
      <div>
        <div class="pas-title">旧密码</div>
        <el-input
          v-model="resetPassword"
          placeholder="请输入当前密码"
          show-password
          autofocus
        ></el-input>
      </div>
      <div>
        <div class="pas-title">新密码</div>
        <el-input
          v-model="newPassword"
          placeholder="请输入要设置的新密码"
          show-password
          autofocus
        ></el-input>
      </div>
      <div>
        <div class="pas-title">确认新密码</div>
        <el-input
          v-model="successPassword"
          placeholder="请确认新密码"
          show-password
          autofocus
        ></el-input>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="resetPasswordDialog = false">取消</el-button>
        <el-button
          type="primary"
          size="small"
          :disabled="!successPassword"
          @click="SYSTEM_SUBMIT"
        >确定</el-button>
      </span>
    </el-dialog>
    <el-dialog
      :visible.sync="systemDialog"
      :title="title"
      :append-to-body="true"
      custom-class="systemDialog"
    >
      <div class="img">
        <img src="@/assets/images/system-log.svg" alt="" />
      </div>
      <div class="text">
        <div>版权所有©极客信安（北京）科技有限公司。保留所有权利。</div>
        <div>流量安全审计系统</div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="systemDialog = false">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { shutdown, reboot, reset_password } from "@/api/system";
import { removeToken } from "@/utils/auth";
import { resetRouter } from "@/router";
export default {
  name: "Navbar",
  data() {
    return {
      passwordDialog: false,
      title: "",
      password: "",
      msgdot: false,
      resetPasswordDialog: false,
      resetPassword: "",
      newPassword: "",
      successPassword: "",
      systemDialog: false,
      confirmText: "",
    };
  },
  computed: {
    downdot() {
      return this.$store.state.conversational.downdot;
    },
  },
  methods: {
    HANDLE_COMMAND(command) {
      switch (command) {
      case "a":
        this.title = "关闭主机";
        this.passwordDialog = true;
        this.confirmText = "关机";
        break;
      case "b":
        this.title = "重启主机";
        this.passwordDialog = true;
        this.confirmText = "重启";
        break;
      case "c":
        this.title = "修改密码";
        this.resetPasswordDialog = true;
        break;
      case "d":
        this.LOG_OUT();
        break;
      case "e":
        this.title = "关于系统";
        this.systemDialog = true;
        break;
      default:
        break;
      }
    },
    SYSTEM_SUBMIT() {
      switch (this.title) {
      case "关闭主机":
        shutdown({
          password: this.password,
        }).then((res) => {
          if (res.err === 0) {
            this.$message.success("已关闭主机");
            this.passwordDialog = false;
          }
        });
        break;
      case "重启主机":
        reboot({
          password: this.password,
        }).then((res) => {
          if (res.err === 0) {
            this.$message.success("已进行重启");
            this.passwordDialog = false;
          }
        });
        break;
      case "修改密码":
        if (this.resetPassword == "") {
          this.$message.warning("旧密码不能为空");
        } else if (this.newPassword === this.successPassword) {
          reset_password({
            password: this.resetPassword,
            new_password: this.newPassword,
          }).then((res) => {
            if (res.err === 0) {
              this.$message.success("密码修改成功");
              this.resetPasswordDialog = false;
              this.resetPassword = "";
              this.newPassword = "";
              this.LOG_OUT();
            }
          });
        } else {
          this.$message.warning("确认密码不一致");
        }

        break;
      default:
        break;
      }
    },
    // 退出登录
    LOG_OUT() {
      this.$confirm("是否退出登录？", {
        confirmButtonText: "退出",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then((res) => {
          removeToken(); // must remove  token  first
          resetRouter();
          this.$store.commit("user/RESET_STATE");
          this.$store.commit("long/GET_Dictus", {});
          this.$store.commit("conversational/tagseachlist", []);
          this.$message.info("已退出登录");
          this.$router.push("/login");
        })
        .catch(() => {});
    },
    // 打开下载抽屉
    opendownDrawer() {
      this.$store.commit("conversational/downDrawerData", true);
      this.$store.commit("conversational/setdowndot", false);
      // if(this.downdot){
      //   this.$store.commit("conversational/downDrawerData", true);
      // }else{
      //   console.log("目前没有下载任务")
      // }
    },
    // 查看任务导入进度
    TASK_DOWN() {
      console.log(this.$store.state.conversational.taskdownShow);
      this.$store.commit("conversational/taskdownShow", true);
    },
  },
};
</script>

<style lang="scss" scoped>
.download-button {
  border: 1px solid #CECECE; /* Light grey border */
  padding: 5px 10px; /* Padding around text and icon */
  border-radius: 4px; /* Rounded corners */
  color:#2C2C35;
  padding: 5px 16px;
  width: 108px !important;
  height: 32px !important;
  font-size: 14px;
}
.icon-style {
  color:#767684;
  /* Adjust your SVG icon style here */
  margin-right: 5px; /* Space between icon and text */
  vertical-align: middle; /* Align icon with text */
  width: 16px !important;
  height: 16px !important;
}
.navbar {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  align-items: center;
  padding: 0 21px;
  padding-left: 0;
  box-sizing: border-box;

  .left {
    width: 160px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background: linear-gradient(180deg, #3787ff 0%, #116ef9 100%);
    border-right: 1px solid #f2f3f7;
    color:#fff;
    font-size: 16px;
    // > img {
    //   width: 75%;
    //   height: 54%;
    //   background-color: transparent;
    // }
  }

  .name {
    margin-right: auto;
    font-size: 14px;
    color: #116ef9;
    margin-left: 16px;
    // background-image: -webkit-linear-gradient(bottom, #1198e1, #17d6b0);
    // -webkit-background-clip: text;
    // -webkit-text-fill-color: transparent;
  }

  .right {
    display: flex;
    align-items: center;

    .msg {
      .downloadText{
        font-size:14px;
      }
      position: relative;
      margin-right: 10px;
      cursor: pointer;

      .el-badge {
        .downicon {
          width: 20px;
          height: 20px;
          margin-left: 10px;
          margin-right: 24px;
          cursor: pointer;

          ::v-deep {
            .el-badge__content.is-fixed {
              top: 3px;
            }
          }
        }
      }

      .msgicon {
        margin-top: 4px;
        margin-right: 24px;
      }

      .down {
        width: 18px;
        height: 18px;
      }

      .el-button {
        font-size: 12px;
        display: flex;
        align-items: center;
        height: 20px;
      }
    }

    .title {
      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
      color: #767684;
    }

    &-box {
      display: flex;
      align-items: center;
    }

    .Userbox {
      cursor: pointer;
      display: flex;
      flex-direction: row;
      align-items: center;

      img {
        margin-right: 8px;
        width: 24px;
        height: 24px;
      }

      .Username {
        display: flex;
        flex-direction: column;

        .adminbox {
          display: flex;
          flex-direction: column;
          align-items: center;
        }
      }
    }
  }
}

.el-dialog__wrapper.system-diglog {
  .el-dialog {
    width: 613px;
    height: 313px;
    box-shadow: 0px 6px 12px rgba(0, 0, 0, 0.08);
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .el-dialog__header {
      width: 100%;
      height: 20px;
      padding: 12px 16px 0 16px;

      > div {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;

        > div:nth-of-type(1) {
          color: #0f0f13;
          font-size: 14px;
          margin-right: 10px;
        }

        > div:nth-of-type(2) {
          color: #116ef9;
          margin-right: auto;
          font-size: 8px;
          cursor: pointer;
        }

        > div:nth-of-type(3) {
          font-size: 14px;
          color: #2c2c35;
        }
      }
    }

    .el-dialog__body {
      padding: 0;
      display: flex;
      justify-content: center;

      .upload-demo {
        width: 517px;
        height: 192px;
        margin: 0;
        display: flex;
        justify-content: center;

        .upload-file-icon {
          width: 69px;
          height: 45px;
          margin-top: 50px;
        }

        .el-upload.el-upload--text {
          width: 100%;
          height: 100%;

          .el-upload-dragger {
            width: 100%;
            height: 100%;
            margin: 0;

            .el-upload__text {
              color: #0f0f13;

              em {
                color: #116ef9;
              }
            }

            .text-maxsize {
              font-size: 10px;
              margin-top: 7px;
              color: #0f0f13;
            }
          }
        }
      }

      .pas-title {
        font-size: 14px;
        color: #0f0f13;
        margin-top: 10px;
        margin-bottom: 10px;
      }

      .upload-timing {
        .uploading-box {
          width: 517px;
          height: 76px;
          border: 1px solid #f2f3f7;
          box-sizing: border-box;
          border-radius: 8px;
          padding: 0 24px;
          padding-bottom: 16px;
          display: flex;
          flex-direction: column;
          justify-content: flex-end;
          position: relative;

          .el-progress-bar__outer {
            height: 6px !important;
          }

          > div:nth-of-type(1) {
            font-size: 14px;
            margin-bottom: 4px;
            padding-right: 50px;
            box-sizing: border-box;

            > span:nth-of-type(1) {
              color: #116ef9;
            }

            > span:nth-of-type(2) {
              color: #0f0f13;
            }
          }

          > div:nth-of-type(2) {
          }

          .success-icon {
            width: 38px;
            height: 38px;
            font-size: 38px;
            position: absolute;
            top: 8px;
            right: 8px;
          }
        }

        > div:nth-of-type(2) {
          margin-top: 8px;
          font-size: 10px;
        }
      }
    }

    .el-dialog__footer {
      padding: 0 16px 16px 16px;

      .cancel {
        width: 78px;
        height: 32px;
        background: #ffffff;
        border: 1px solid #f2f3f7;
        box-sizing: border-box;
        border-radius: 4px;
        color: #0f0f13;
        padding: 0;
      }

      .submit {
        width: 78px;
        height: 32px;
        background: #116ef9;
        border-radius: 4px;
        color: #ffffff;
        padding: 0;
      }

      .submit-d {
        width: 78px;
        height: 32px;
        background: #cecece;
        border-radius: 4px;
        color: #ffffff;
        padding: 0;
        border: 0;
        // cursor: not-allowed;
        pointer-events: none;
      }
    }
  }
}

::v-deep .systemDialog {
  width: 460px;
  background: #ffffff;
  box-shadow: 0px 6px 18px rgba(45, 47, 51, 0.14);
  border-radius: 8px;
  padding-top: 16px;

  .el-dialog__header {
    padding: 0;
    margin-bottom: 24px;
    padding: 0px 24px;
  }

  .el-dialog__title {
    color: #2c2c35;
    font-weight: 600;
  }

  .el-dialog__body {
    padding: 0px 24px;

    .img {
      width: 217px;
      height: 53px;
      margin-bottom: 24px;

      > img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .text {
      color: #2c2c35;
      font-size: 14px;
      margin-bottom: 24px;

      > div:nth-of-type(1) {
        font-weight: 600;
        margin-bottom: 8px;
      }
    }
  }

  .el-dialog__footer {
    border-top: 1px solid #f2f3f7;
    padding: 12px 24px;

    .el-button--primary {
      background: #116ef9;
      border-radius: 4px;
    }

    .el-button--primary:hover {
      color: #fff;
      background-color: #409eff;
      border-color: #409eff;
    }
  }
}
.icon-a-16_alert {
  color: red;
}
::v-deep {
  .el-dialog {
    border-radius: 8px !important;
  }
  .el-dialog__body {
    padding: 24px 16px 32px;
  }
}
.tip {
  margin-bottom: 4px;
  color: #2c2c35;
}
</style>