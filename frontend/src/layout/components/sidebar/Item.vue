<script>
export default {
  name: "MenuItem",
  functional: true,
  props: {
    icon: {
      type: String,
      default: "",
    },
    title: {
      type: String,
      default: "",
    },
    iconClass:{
      type:String,
      default:''
    }
  },
  render(h, context) {
    const { icon, title,iconClass } = context.props;
    const vnodes = [];
    if(iconClass){
      vnodes.push(<i class={['iconfont', iconClass]} />);
    }else if
    (icon) {
      if (icon.includes("el-icon")) {
        vnodes.push(<i class={[icon, "sub-el-icon"]} />);
      } else {
        vnodes.push(<svg-icon icon-class={icon} style="font-size:24px;" />);
      }
    }

    if (title) {
      vnodes.push(<span slot="title">{title}</span>);
    }
    return vnodes;
  },
};
</script>

<style scoped>
.sub-el-icon {
  color: currentColor;
  width: 1em;
  height: 1em;
}
.iconfont{
  font-size: 24px;
  margin-left: 16px;
  margin-right: 12px;
}
</style>
