<template>
  <div class="sidebar">
    <el-menu :default-active="activeMenu" :unique-opened="false" :collapse-transition="true" mode="vertical"
             @select="handleSelect"
    >
      <sidebar-item v-for="route in routes" :key="route.path" :active-menu="activeMenu" :item="route" :base-path="route.path" />
      <section v-if="$isTraffic" class="diy-item" @click="SEARCH">
        <i
          class="iconfont icon-nav_Relationship_line"
        ></i>
        <div>图探索</div>
      </section>
    </el-menu>
  </div>
</template>

<script>

import SidebarItem from './SidebarItem';
export default {
  name: 'Sidebar',
  components: {
    SidebarItem
  },
  computed: {
    activeMenu () {
      const route = this.$route;
      const { meta, path } = route;
      // 如果设置了path，侧边栏将突出显示设置的路径
      if (meta.activeMenu) {
        return meta.activeMenu;
      }
      return path;
    },
    routes () {
      return this.$router.options.routes;
    },
  },
  methods: {
    handleSelect (key, keyPath) {
      this.$store.commit("app/setTagchang", key);
    },
    // 图探索的跳转
    SEARCH () {
      const url = this.$router.resolve({ path: '/search' });
      window.open(url.href, '_blank');
    }
  }
};
</script>

<style lang="scss">
@import '~@/styles/variables.scss';

.sidebar {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  position: relative;
}

.el-menu {
  border: 0;
}

.el-menu-item {
  padding: 0px !important;
  font-size: 14px;
  height: 48px;
  line-height: 0px;
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  position: relative;

  .svg-icon {
    color: #767684;
    margin-left: 16px;
    margin-right: 12px;
  }
}

.el-menu-item.is-active {
  background-color: #ecf5ff !important;

  >span {
    // color: #000;
  }
}

.el-menu-item.is-active {
  color: #116ef9;
  font-weight: bold;

  .svg-icon {
    color: #116EF9;
  }
}

.el-menu-item.is-active::after {
  content: ' ';
  width: 1px;
  height: 100%;
  background: #116ef9;
  border-radius: 0px 2px 2px 0px;
  position: absolute;
  left: 0;
  top: 0;
}

.diy-item {
  height: 48px;
  font-size: 14px;
  display: flex;
  align-items: center;
  color: #303133;
  cursor: pointer;
  border-top: 1px solid #f2f3f7;
  transition: border-color 0.3s, background-color 0.3s, color 0.3s;

  .icon {
    font-size: 24px;
    margin-left: 16px;
    margin-right: 12px;
  }
}

.diy-item:hover {
  background-color: #ecf5ff;
}
.icon-nav_Relationship_line{
  font-size: 24px;
    margin-left: 16px;
    margin-right: 12px;
}
</style>